package com.wshoto.jiali;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Optional;


/**
 * JsonUtils is a utility class for converting objects to json strings and vice versa.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Sam Zhang
 * Created Date - 2024/6/14 上午11:16
 */
@Slf4j
@Getter
@RequiredArgsConstructor
public enum JsonUtils {
    /**
     * Jackson ObjectMapper instance.
     */
    JACKSON(createObjectMapper());

    /**
     * Local date time pattern.
     */
    public static final String LOCAL_DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    private static final DateTimeFormatter LOCAL_DATE_TIME_FORMATTER =
            DateTimeFormatter.ofPattern(LOCAL_DATE_TIME_PATTERN)
                             .withZone(ZoneId.systemDefault());

    private final ObjectMapper objectMapper;

    public static String toJson(Object o) {
        return JACKSON.objToStr(o);
    }

    public static <T> Optional<T> fromJson(String str, Class<T> clazz) {
        return JACKSON.strToObj(str, clazz);
    }

    public static <T> Optional<T> fromJsonV2(String str, TypeReference<T> typeReference) {
        return JACKSON.strToObj(str, typeReference);
    }

    private static ObjectMapper createObjectMapper() {
        return new Jackson2ObjectMapperBuilder().serializationInclusion(JsonInclude.Include.NON_NULL)
                                                .failOnUnknownProperties(false)
                                                .failOnEmptyBeans(false)
                                                .featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                                                .serializerByType(LocalDateTime.class, new LocalDateTimeSerializer(
                                                        LOCAL_DATE_TIME_FORMATTER))
                                                .serializerByType(LocalDate.class, new LocalDateSerializer(
                                                        DateTimeFormatter.ISO_LOCAL_DATE))
                                                .deserializerByType(LocalDate.class, new LocalDateDeserializer(
                                                        DateTimeFormatter.ISO_LOCAL_DATE))
                                                .deserializerByType(LocalDateTime.class, new LocalDateTimeDeserializer(
                                                        LOCAL_DATE_TIME_FORMATTER))
                                                .serializerByType(LocalTime.class, new LocalTimeSerializer(
                                                        DateTimeFormatter.ISO_LOCAL_TIME))
                                                .deserializerByType(LocalTime.class, new LocalTimeDeserializer(
                                                        DateTimeFormatter.ISO_LOCAL_TIME))
                                                .build();
    }

    private String objToStr(Object object) {
        try {
            return this.objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Write object to string occurs an error", e);
            return Strings.EMPTY;
        }
    }

    private <T> Optional<T> strToObj(String str, TypeReference<T> typeReference) {
        if (str.isBlank()) {
            return Optional.empty();
        }
        try {
            return Optional.of(this.objectMapper.readValue(str, typeReference));
        } catch (JsonProcessingException e) {
            log.error("Read json string {} to type {} error", str, typeReference, e);
            return Optional.empty();
        }
    }

    private <T> Optional<T> strToObj(String str, Class<T> clazz) {
        if (str.isBlank()) {
            return Optional.empty();
        }
        try {
            return Optional.of(this.objectMapper.readValue(str, clazz));
        } catch (JsonProcessingException e) {
            log.error("Read json string {} to type {} error", str, clazz);
            return Optional.empty();
        }
    }

}
