package com.wshoto.jiali.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Getter
public enum GenderEnum {

    /**
     * 性别
     */
    MALE("1", "M"),
    FEMALE("2", "F");

    private String code;
    private String message;

    GenderEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    //性别转换
    public  static String getGender(String message) {
        if (StringUtils.isBlank(message)){
            return "";
        }
        return Objects.equals(message,GenderEnum.MALE.message)?GenderEnum.MALE.code:GenderEnum.FEMALE.code;

    }
}
