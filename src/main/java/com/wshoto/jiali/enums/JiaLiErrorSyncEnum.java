package com.wshoto.jiali.enums;

import lombok.Getter;

@Getter
public enum JiaLiErrorSyncEnum {

    /**
     * 数据状态
     */
    USER_ADD(1, "员工新增"),
    USER_UPDATE(2, "员工修改"),
    DEPT_ADD(3, "部门新增"),
    DEPT_UPDATE(4, "部门修改"),
    USER_DEPT_ADD(5,"员工部门关系新增"),
    USER_DEPT_UPDATE(6,"员工部门关系修改");
    private Integer code;
    private String message;

    JiaLiErrorSyncEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

}
