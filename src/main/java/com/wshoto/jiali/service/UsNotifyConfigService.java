package com.wshoto.jiali.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.wshoto.jiali.domain.UsNotifyConfig;

/**
* <AUTHOR>
* @description 针对表【us_notify_config(同步通知配置)】的数据库操作Service
* @createDate 2024-02-23 14:27:40
*/
public interface UsNotifyConfigService extends IService<UsNotifyConfig> {

    /**
     * 邮件通知配置保存
     */
    Boolean saveUsNotifyConfig();

    int setNotifyEmails(String emails);

    int deleteNotifyConfig();

}
