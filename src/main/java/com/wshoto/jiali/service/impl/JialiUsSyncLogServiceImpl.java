package com.wshoto.jiali.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wshoto.jiali.domain.JialiUsSyncLog;
import com.wshoto.jiali.mapper.JialiUsSyncLogMapper;
import com.wshoto.jiali.service.JialiUsSyncLogService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【jiali_us_sync_log(数据清洗入库异常日志)】的数据库操作Service实现
* @createDate 2024-02-22 16:22:36
*/


@DS("db_qa_wecom")
@Service
public class JialiUsSyncLogServiceImpl extends ServiceImpl<JialiUsSyncLogMapper, JialiUsSyncLog>
        implements JialiUsSyncLogService {

}




