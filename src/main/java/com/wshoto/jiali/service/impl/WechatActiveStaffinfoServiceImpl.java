package com.wshoto.jiali.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.wshoto.jiali.config.JiaLiConfig;
import com.wshoto.jiali.domain.Attrs;
import com.wshoto.jiali.domain.EmailSendCommandVo;
import com.wshoto.jiali.domain.Extattr;
import com.wshoto.jiali.domain.JialiUsSyncLog;
import com.wshoto.jiali.domain.Text;
import com.wshoto.jiali.domain.UsDepartment;
import com.wshoto.jiali.domain.UsDepartmentUser;
import com.wshoto.jiali.domain.UsUser;
import com.wshoto.jiali.domain.WechatActiveStaffinfo;
import com.wshoto.jiali.domain.WechatInactiveExcludeStaffinfo;
import com.wshoto.jiali.domain.WechatOrgchart;
import com.wshoto.jiali.domain.WechatStuffDTO;
import com.wshoto.jiali.enums.DataStatusEnum;
import com.wshoto.jiali.enums.DeleteStatusEnum;
import com.wshoto.jiali.enums.GenderEnum;
import com.wshoto.jiali.enums.JiaLiErrorSyncEnum;
import com.wshoto.jiali.enums.SyncStatusEnum;
import com.wshoto.jiali.enums.ToInviteEnum;
import com.wshoto.jiali.enums.UserEnableEnum;
import com.wshoto.jiali.feign.clients.MessageClient;
import com.wshoto.jiali.mapper.UsUserMapper;
import com.wshoto.jiali.mapper.WechatActiveStaffinfoMapper;
import com.wshoto.jiali.service.JialiUsSyncLogService;
import com.wshoto.jiali.service.UsDepartmentService;
import com.wshoto.jiali.service.UsDepartmentUserService;
import com.wshoto.jiali.service.UsUserService;
import com.wshoto.jiali.service.WechatActiveStaffinfoService;
import com.wshoto.jiali.service.WechatInactiveExcludeStaffinfoService;
import com.wshoto.jiali.service.WechatInactiveStaffinfoService;
import com.wshoto.jiali.service.WechatOrgchartService;
import com.wshoto.jiali.service.WechatStaffDTOService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 员工同步
 *
 * <AUTHOR>
 * @description 针对表【wechat_active_staffinfo】的数据库操作Service实现
 * @createDate 2024-01-29 17:06:01
 */
@Slf4j
@DS("db_qa_wecom_datasync")
@Service
public class WechatActiveStaffinfoServiceImpl extends ServiceImpl<WechatActiveStaffinfoMapper, WechatActiveStaffinfo>
        implements WechatActiveStaffinfoService {

    @Value("${email.send-to-list}")
    private List<String> emailSendToList;

    @Autowired
    private TemplateEngine templateEngine;

    @Autowired
    private JiaLiConfig jiaLiConfig;

    @Autowired
    private MessageClient messageClient;

    @Autowired
    private WechatStaffDTOService wechatStaffDTOService;

    @Autowired
    private WechatInactiveStaffinfoService wechatInactiveStaffinfoService;

    @Autowired
    private WechatInactiveExcludeStaffinfoService wechatInactiveExcludeStaffinfoService;

    @Autowired
    private WechatOrgchartService wechatOrgchartService;

    @Autowired
    private UsUserService userService;

    @Autowired
    private UsDepartmentService usDepartmentService;

    @Autowired
    private UsDepartmentUserService usDepartmentUserService;

    @Autowired
    private JialiUsSyncLogService jialiUsSyncLogService;

    @Autowired
    private UsUserMapper usUserMapper;

    @Autowired
    private Environment environment;

    private static final String EXTERNALPOSITION = "员工";

    private static final Integer TYPE = 0; // 属性类型：0-文本

    private static final String EXTATTRNAME = "职务(中)";

    private static final String EXTERNALCORPNAME = "嘉里(中国)项目管理有限公司上海分公司";

    /**
     * 同步员工信息
     */
    @Override
    public void syncStaffInfo() {
        log.info("开始执行员工信息同步流程");
        // 部门白名单配置，根据L2、L8层部门取并集
        List<String> deptsL2 = jiaLiConfig.getWhiteDeptL2();
        log.info("同步员工信息-获取L2层部门白名单：{}", JSON.toJSONString(deptsL2));

        List<WechatOrgchart> wechatOrgchartList = wechatOrgchartService.lambdaQuery()
                                                                       .in(WechatOrgchart::getL2DivisionCode, deptsL2)
                                                                       .list();
        log.info("同步员工信息-根据L2层部门白名单查询到组织架构数量：{}",
                 CollectionUtils.isEmpty(wechatOrgchartList) ? 0 : wechatOrgchartList.size());

        List<String> deptsByL2 = CollectionUtils.isEmpty(wechatOrgchartList) ? Lists.newArrayList() :
                wechatOrgchartList.stream()
                                  .map(WechatOrgchart::getL8DepartmentCode)
                                  .filter(StringUtils::isNotEmpty)
                                  .collect(Collectors.toList());
        log.info("同步员工信息-从L2层部门获取的L8部门列表：{}", JSON.toJSONString(deptsByL2));

        List<String> deptsByL8 = jiaLiConfig.getWhiteDept();
        log.info("同步员工信息-配置的L8层部门白名单：{}", JSON.toJSONString(deptsByL8));

        deptsByL2.addAll(deptsByL8);
        log.info("同步员工信息-合并L2和L8获取的部门列表(去重前)：{}", JSON.toJSONString(deptsByL2));

        List<String> depts = deptsByL2.stream()
                                      .filter(StringUtils::isNotBlank)
                                      .distinct()
                                      .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(depts)) {
            log.info("同步员工信息-部门白名单为空，无法同步员工信息");
            return;
        }
        log.info("同步员工信息-最终部门白名单(去重后)：{},大小:{}", JSON.toJSONString(depts), depts.size());

        // 读取SF员工信息 过滤AD Username为空的数据
        List<WechatStuffDTO> aadStuffs = wechatStaffDTOService.lambdaQuery()
                                                              .in(WechatStuffDTO::getDepartment, depts)
                                                              .isNotNull(WechatStuffDTO::getAdUsername)
                                                              .isNotNull(WechatStuffDTO::getAadId)
                                                              .list();
        log.info("同步员工信息-根据部门白名单查询到的员工数量(过滤前)：{}", aadStuffs.size());

        // 过滤掉KPL.SF结尾的无效用户
        int beforeFilterSize = aadStuffs.size();
        aadStuffs = aadStuffs.stream()
                             .filter(info -> !StringUtils.endsWith(info.getAdUsername(), "@KPL.SF"))
                             .collect(Collectors.toList());
        log.info("同步员工信息-过滤KPL.SF结尾的无效用户：过滤前{}个，过滤后{}个，过滤掉{}个", beforeFilterSize,
                 aadStuffs.size(), beforeFilterSize - aadStuffs.size());

        log.info("同步员工信息-在职员工大小：{}", aadStuffs.size());
        if (CollectionUtils.isEmpty(aadStuffs)) {
            log.info("同步员工信息-根据部门白名单查询员工信息为空，无法同步员工信息");
            return;
        }

        log.info("同步员工信息-开始筛选在职员工");

        // 筛选在职员工
        List<WechatStuffDTO> wechatActiveStuffDTOS = aadStuffs.stream()
                                                              .filter(staff -> DeleteStatusEnum.NOT_DELETED.getCode()
                                                                                                           .equals(staff.getDeleted()))
                                                              .toList();
        log.info("同步员工信息-在职员工筛选完成：总员工{}个，在职员工{}个", aadStuffs.size(),
                 wechatActiveStuffDTOS.size());

        // 记录在职员工的关键信息
        wechatActiveStuffDTOS.forEach(staff -> log.info(
                "同步员工信息-员工详情：aadId={}, adUsername={}, 中文名={}, 英文名={}, 部门={}, 职务={}",
                staff.getAadId(), staff.getAdUsername(), staff.getStaffChineseName(), staff.getStaffEnglishName(),
                staff.getDepartment(), staff.getEnglishTitle()));

        // 构造Map：id -> identity
        log.info("同步员工信息-开始构建员工映射Map");
        Map<String, WechatStuffDTO> wechatActiveStuffMap = wechatActiveStuffDTOS.stream()
                                                                                .collect(Collectors.toMap(
                                                                                        WechatStuffDTO::getAadId,
                                                                                        Function.identity(),
                                                                                        (o, n) -> o));
        log.info("同步员工信息-员工映射Map构建完成，大小：{}", wechatActiveStuffMap.size());

        Map<String, WechatStuffDTO> wechatDeletedStaffInfoMap = aadStuffs.stream()
                                                                         .filter(staff -> DeleteStatusEnum.DELETED.getCode()
                                                                                                                  .equals(staff.getDeleted()))
                                                                         .collect(Collectors.toMap(
                                                                                 WechatStuffDTO::getAadId,
                                                                                 Function.identity(), (o, n) -> o));
        log.info("同步员工信息-离职员工：{}， 大小：{}", JSON.toJSONString(wechatDeletedStaffInfoMap),
                 wechatDeletedStaffInfoMap.size());

        // 读取db获取微信部门信息
        List<UsDepartment> whitesList = usDepartmentService.lambdaQuery()
                                                           .in(UsDepartment::getSourceDeptId, depts)
                                                           .eq(UsDepartment::getDeleted,
                                                               DeleteStatusEnum.NOT_DELETED.getCode())
                                                           .list();
        log.info("同步员工信息-从数据库获取到的部门信息数量：{}", whitesList.size());

        List<Integer> wxTargetDeptIds = whitesList.stream()
                                                  .map(UsDepartment::getTargetDeptId)
                                                  .filter(Objects::nonNull)
                                                  .map(Integer::valueOf)
                                                  .distinct()
                                                  .collect(Collectors.toList());
        log.info("同步员工信息-获取到的微信目标部门ID列表：{}", JSON.toJSONString(wxTargetDeptIds));

        // 根据微信部门id，查找db员工全部信息
        List<UsUser> dbUserList = userService.lambdaQuery()
                                             .in(UsUser::getMainDepartment, wxTargetDeptIds)
                                             .eq(UsUser::getDeleted, DeleteStatusEnum.NOT_DELETED.getCode())
                                             .list();
        log.info("同步员工信息-从数据库获取本地员工信息，数量：{}", dbUserList.size());

        // 构建员工映射，用于快速查找员工信息
        log.info("同步员工信息-开始构建员工映射Map");
        Map<String, UsUser> dbUserMap = dbUserList.stream()
                                                  .collect(Collectors.toMap(UsUser::getAadId, Function.identity(),
                                                                            (o, n) -> o));
        log.info("同步员工信息-员工映射Map构建完成，大小：{}", dbUserMap.size());

        // 记录员工映射中的关键信息
        dbUserMap.forEach((aadId, user) -> log.info(
                "同步员工信息-员工映射详情：aadId={}, name={}, sourceUserid={}, mainDepartment={}, enable={}", aadId,
                user.getName(), user.getSourceUserid(), user.getMainDepartment(), user.getEnable()));

        List<UsUser> addUsUserList = Lists.newLinkedList();
        List<UsUser> updateUsUserList = Lists.newLinkedList();
        List<UsDepartmentUser> addUsDepartmentUserList = Lists.newLinkedList();
        List<UsDepartmentUser> updateUsDepartmentUserList = Lists.newLinkedList();

        int disabledUserCount = 0;
        for (UsUser dbUsUser : dbUserList) {
            String dbUsUserAadID = dbUsUser.getAadId();
            if (wechatDeletedStaffInfoMap.containsKey(dbUsUserAadID)) {
                log.info("同步员工信息-发现需要禁用/删除的员工，AadId：{}，员工信息：{}", dbUsUserAadID,
                         JSON.toJSONString(dbUsUser));
                dbUsUser.setDataStatus(DataStatusEnum.DELETE.getCode());
                dbUsUser.setSyncStatus(SyncStatusEnum.INIT.getCode());
                dbUsUser.setGmtModified(DateUtil.date());
                updateUsUserList.add(dbUsUser);
                disabledUserCount++;
            }
        }
        log.info("同步员工信息-共发现{}个需要禁用/删除的员工", disabledUserCount);

        log.info("同步员工信息-inac有,ac没有,中间表有,需要禁用/删除的:{}", JSON.toJSONString(updateUsUserList));

        List<UsDepartment> usDepartmentList = usDepartmentService.list();
        log.info("同步员工信息-从数据库获取所有部门信息，数量：{}", usDepartmentList.size());

        log.info("同步员工信息-开始构建部门映射Map");
        Map<String, UsDepartment> usDepartmentMap = usDepartmentList.stream()
                                                                    .collect(Collectors.toMap(
                                                                            UsDepartment::getSourceDeptId,
                                                                            Function.identity(),
                                                                            (o, n) -> n.getDeleted() >= o.getDeleted() ?
                                                                                    o : n));
        log.info("同步员工信息-部门映射Map构建完成，大小：{}", usDepartmentMap.size());

        // 记录部门映射中的关键信息
        usDepartmentMap.forEach(
                (sourceId, dept) -> log.info("同步员工信息-部门映射详情：sourceId={}, targetId={}, name={}, deleted={}",
                                             sourceId, dept.getTargetDeptId(), dept.getName(), dept.getDeleted()));

        // 新增或修改
        int updateCount = 0;
        int addCount = 0;
        for (WechatStuffDTO wechatStuffDTO : wechatActiveStuffDTOS) {
            if (dbUserMap.containsKey(wechatStuffDTO.getAadId())) {
                log.info("同步员工信息-发现需要更新的员工，AadId：{}，员工信息：{}", wechatStuffDTO.getAadId(),
                         JSON.toJSONString(wechatStuffDTO));
                // 获取需要修改的数据
                getNeedUpdateUserList(dbUserMap, updateUsUserList, updateUsDepartmentUserList, wechatStuffDTO);
                updateCount++;
            } else {
                log.info("同步员工信息-发现需要新增的员工，AadId：{}，员工信息：{}", wechatStuffDTO.getAadId(),
                         JSON.toJSONString(wechatStuffDTO));
                // 获取员工新增
                getAddUserList(addUsUserList, addUsDepartmentUserList, wechatStuffDTO, usDepartmentMap);
                addCount++;
            }
        }
        log.info("同步员工信息-处理完成：需要更新{}个员工，需要新增{}个员工", updateCount, addCount);

        int resetCount = 0;
        for (UsUser usUser : dbUserList) {
            if (Objects.equals(usUser.getDataStatus(), DataStatusEnum.INSERT.getCode()) &&
                    Objects.equals(usUser.getSyncStatus(), SyncStatusEnum.FAIL.getCode())) {
                log.info("同步员工信息-发现需要重置状态的员工，员工信息:{}", JSON.toJSONString(usUser));
                usUser.setDataStatus(DataStatusEnum.INSERT.getCode());
                usUser.setSyncStatus(SyncStatusEnum.INIT.getCode());
                updateUsUserList.add(usUser);
                resetCount++;
            }
        }
        log.info("同步员工信息-共发现{}个需要重置状态的员工", resetCount);

        log.info("同步员工信息-开始批量更新数据到数据库");
        batchUpdateToDB(addUsUserList, updateUsUserList, addUsDepartmentUserList, updateUsDepartmentUserList);
        log.info("同步员工信息-员工同步流程执行完成");
    }

    protected List<String> getSfInactiveExcludeStaffAdUsernames() {
        log.info("开始获取离职员工赦免列表");
        List<WechatInactiveExcludeStaffinfo> sfInactiveExcludeStaffInfoList =
                wechatInactiveExcludeStaffinfoService.list();
        log.info("同步员工信息-离职员工赦免表：[{}]", sfInactiveExcludeStaffInfoList);

        List<String> adUsernames = sfInactiveExcludeStaffInfoList.stream()
                                                                 .map(WechatInactiveExcludeStaffinfo::getAdUsername)
                                                                 .filter(StringUtils::isNotEmpty)
                                                                 .toList();
        log.info("同步员工信息-离职员工赦免表中的有效AD用户名数量：{}", adUsernames.size());
        return adUsernames;
    }

    @Override
    public UsUser manuallyDisableStaff(String adUserName) {
        log.info("开始手动禁用员工，AD用户名：{}", adUserName);
        UsUser dbUsUser = userService.lambdaQuery()
                                     .eq(UsUser::getSourceUserid, adUserName)
                                     .eq(UsUser::getDeleted, DeleteStatusEnum.NOT_DELETED.getCode())
                                     .list()
                                     .get(0);
        log.info("手动禁用员工-查询到员工信息：{}", JSON.toJSONString(dbUsUser));

        // 更新为禁用状态
        dbUsUser.setEnable(UserEnableEnum.DISABLE.getCode());
        log.info("手动禁用员工-设置员工状态为禁用：{}", UserEnableEnum.DISABLE.getCode());

        if (Objects.equals(dbUsUser.getDataStatus(), DataStatusEnum.INSERT.getCode()) &&
                Objects.equals(dbUsUser.getSyncStatus(), SyncStatusEnum.FAIL.getCode())) {
            dbUsUser.setDataStatus(DataStatusEnum.INSERT.getCode());
            log.info("手动禁用员工-员工为新增失败状态，保持数据状态为新增：{}", DataStatusEnum.INSERT.getCode());
        } else {
            dbUsUser.setDataStatus(DataStatusEnum.UPDATE.getCode());
            log.info("手动禁用员工-设置数据状态为更新：{}", DataStatusEnum.UPDATE.getCode());
        }
        dbUsUser.setSyncStatus(SyncStatusEnum.INIT.getCode());
        dbUsUser.setGmtModified(DateUtil.date());
        log.info("手动禁用员工-设置同步状态为初始化：{}，更新修改时间：{}", SyncStatusEnum.INIT.getCode(),
                 DateUtil.date());

        // save to DB
        log.info("手动禁用员工-开始更新员工信息到数据库");
        userService.updateById(dbUsUser);
        log.info("手动禁用员工-员工信息更新完成");
        return dbUsUser;
    }

    @Override
    public UsUser manuallyExitedStaff(String adUserName) {
        log.info("开始手动设置员工为离职状态，AD用户名：{}", adUserName);
        UsUser dbUsUser = userService.lambdaQuery()
                                     .eq(UsUser::getSourceUserid, adUserName)
                                     .eq(UsUser::getDeleted, DeleteStatusEnum.NOT_DELETED.getCode())
                                     .list()
                                     .get(0);
        log.info("手动设置员工离职-查询到员工信息：{}", JSON.toJSONString(dbUsUser));

        // 需要删除
        if (!dbUsUser.getDataStatus()
                     .equals(DataStatusEnum.DELETE.getCode())) {
            log.info("手动设置员工离职-员工当前数据状态不是删除状态，需要更新");
            dbUsUser.setDataStatus(DataStatusEnum.DELETE.getCode());
            dbUsUser.setSyncStatus(SyncStatusEnum.INIT.getCode());
            dbUsUser.setGmtModified(DateUtil.date());
            log.info("手动设置员工离职-设置数据状态为删除：{}，同步状态为初始化：{}，更新修改时间：{}",
                     DataStatusEnum.DELETE.getCode(), SyncStatusEnum.INIT.getCode(), DateUtil.date());

            log.info("手动设置员工离职-开始更新员工信息到数据库");
            userService.updateById(dbUsUser);
            log.info("手动设置员工离职-员工信息更新完成");
        } else {
            log.info("手动设置员工离职-员工当前已是删除状态，无需更新");
        }
        return dbUsUser;
    }

    /**
     * 批量操作
     *
     * @param addUsUserList
     * @param updateUsUserList
     * @param addUsDepartmentUserList
     */
    private void batchUpdateToDB(List<UsUser> addUsUserList, List<UsUser> updateUsUserList,
                                 List<UsDepartmentUser> addUsDepartmentUserList,
                                 List<UsDepartmentUser> updateUsDepartmentUserList) {
        log.info("开始批量更新数据到数据库");
        List<JialiUsSyncLog> jialiUsSyncLogList = new ArrayList<>();
        log.info("批量更新-初始化同步日志列表");

        log.info("批量更新-开始处理新增员工列表，数量：{}", addUsUserList.size());
        int addUserSuccessCount = 0;
        int addUserFailCount = 0;
        for (UsUser usUser : addUsUserList) {
            try {
                log.info("批量更新-新增员工：{}", JSON.toJSONString(usUser));
                userService.save(usUser);
                addUserSuccessCount++;
                log.info("批量更新-新增员工成功，当前成功数：{}", addUserSuccessCount);
            } catch (Exception e) {
                addUserFailCount++;
                log.error("批量更新-员工新增出现异常, 员工信息：{}", JSON.toJSONString(usUser), e);
                addUserError(jialiUsSyncLogList, usUser, e);
            }
        }
        log.info("批量更新-新增员工处理完成，成功：{}，失败：{}", addUserSuccessCount, addUserFailCount);

        log.info("批量更新-开始处理更新员工列表，数量：{}", updateUsUserList.size());
        int updateUserSuccessCount = 0;
        int updateUserFailCount = 0;
        for (UsUser usUser : updateUsUserList) {
            try {
                log.info("批量更新-更新员工：{}", JSON.toJSONString(usUser));
                usUserMapper.updateUsUser(usUser);
                updateUserSuccessCount++;
                log.info("批量更新-更新员工成功，当前成功数：{}", updateUserSuccessCount);
            } catch (Exception e) {
                updateUserFailCount++;
                log.error("批量更新-员工更新出现异常，员工信息：{}", JSON.toJSONString(usUser), e);
                updateUsererror(jialiUsSyncLogList, usUser, e);
            }
        }
        log.info("批量更新-更新员工处理完成，成功：{}，失败：{}", updateUserSuccessCount, updateUserFailCount);

        log.info("批量更新-开始处理新增部门员工关系列表，数量：{}", addUsDepartmentUserList.size());
        int addDeptUserSuccessCount = 0;
        int addDeptUserFailCount = 0;
        for (UsDepartmentUser usDepartmentUser : addUsDepartmentUserList) {
            try {
                log.info("批量更新-新增部门员工关系：{}", JSON.toJSONString(usDepartmentUser));
                usDepartmentUserService.save(usDepartmentUser);
                addDeptUserSuccessCount++;
                log.info("批量更新-新增部门员工关系成功，当前成功数：{}", addDeptUserSuccessCount);
            } catch (Exception e) {
                addDeptUserFailCount++;
                log.error("批量更新-新增部门员工关系出现异常，关系信息：{}", JSON.toJSONString(usDepartmentUser), e);
                addUsDepartmentUserError(jialiUsSyncLogList, usDepartmentUser, e);
            }
        }
        log.info("批量更新-新增部门员工关系处理完成，成功：{}，失败：{}", addDeptUserSuccessCount, addDeptUserFailCount);

        log.info("批量更新-开始处理更新部门员工关系列表，数量：{}", updateUsDepartmentUserList.size());
        int updateDeptUserSuccessCount = 0;
        int updateDeptUserFailCount = 0;
        for (UsDepartmentUser updateUsDepartmentUser : updateUsDepartmentUserList) {
            try {
                log.info("批量更新-更新部门员工关系：{}", JSON.toJSONString(updateUsDepartmentUser));
                usDepartmentUserService.updateById(updateUsDepartmentUser);
                updateDeptUserSuccessCount++;
                log.info("批量更新-更新部门员工关系成功，当前成功数：{}", updateDeptUserSuccessCount);
            } catch (Exception e) {
                updateDeptUserFailCount++;
                log.error("批量更新-更新部门员工关系出现异常，关系信息：{}", JSON.toJSONString(updateUsDepartmentUser),
                          e);
                updateUsDepartmentUserError(jialiUsSyncLogList, updateUsDepartmentUser, e);
            }
        }
        log.info("批量更新-更新部门员工关系处理完成，成功：{}，失败：{}", updateDeptUserSuccessCount,
                 updateDeptUserFailCount);

        if (!jialiUsSyncLogList.isEmpty()) {
            log.info("批量更新-存在同步错误日志，开始保存错误日志，数量：{}", jialiUsSyncLogList.size());
            jialiUsSyncLogService.saveBatch(jialiUsSyncLogList);
            log.info("批量更新-错误日志保存完成");
        } else {
            log.info("批量更新-无同步错误日志");
        }

        log.info("批量更新数据到数据库完成，总结：新增员工(成功:{},失败:{}), 更新员工(成功:{},失败:{}), " +
                         "新增部门员工关系(成功:{},失败:{}), 更新部门员工关系(成功:{},失败:{})", addUserSuccessCount,
                 addUserFailCount, updateUserSuccessCount, updateUserFailCount, addDeptUserSuccessCount,
                 addDeptUserFailCount, updateDeptUserSuccessCount, updateDeptUserFailCount);
    }

    private static void addUserError(List<JialiUsSyncLog> jialiUsSyncLogList, UsUser usUser, Exception e) {
        JialiUsSyncLog jialiUsSyncLog = new JialiUsSyncLog();
        jialiUsSyncLog.setUserId(usUser.getSourceUserid());
        jialiUsSyncLog.setErrorType(JiaLiErrorSyncEnum.USER_ADD.getCode());
        jialiUsSyncLog.setErrorDesc(e.getMessage());
        jialiUsSyncLog.setErrorJson(JSON.toJSONString(usUser));
        jialiUsSyncLog.setCreateTime(DateUtil.date());
        jialiUsSyncLog.setUpdateTime(DateUtil.date());
        jialiUsSyncLogList.add(jialiUsSyncLog);
    }

    private static void updateUsererror(List<JialiUsSyncLog> jialiUsSyncLogList, UsUser usUser, Exception e) {
        JialiUsSyncLog jialiUsSyncLog = new JialiUsSyncLog();
        jialiUsSyncLog.setUserId(usUser.getSourceUserid());
        jialiUsSyncLog.setErrorType(JiaLiErrorSyncEnum.USER_UPDATE.getCode());
        jialiUsSyncLog.setErrorDesc(e.getMessage());
        jialiUsSyncLog.setErrorJson(JSON.toJSONString(usUser));
        jialiUsSyncLog.setCreateTime(DateUtil.date());
        jialiUsSyncLog.setUpdateTime(DateUtil.date());
        jialiUsSyncLogList.add(jialiUsSyncLog);
    }

    private static void addUsDepartmentUserError(List<JialiUsSyncLog> jialiUsSyncLogList,
                                                 UsDepartmentUser usDepartmentUser, Exception e) {
        JialiUsSyncLog jialiUsSyncLog = new JialiUsSyncLog();
        jialiUsSyncLog.setUserId(usDepartmentUser.getTartgetUserid());
        jialiUsSyncLog.setErrorType(JiaLiErrorSyncEnum.USER_DEPT_ADD.getCode());
        jialiUsSyncLog.setErrorDesc(e.getMessage());
        jialiUsSyncLog.setErrorJson(JSON.toJSONString(usDepartmentUser));
        jialiUsSyncLog.setCreateTime(DateUtil.date());
        jialiUsSyncLog.setUpdateTime(DateUtil.date());
        jialiUsSyncLogList.add(jialiUsSyncLog);
    }

    private static void updateUsDepartmentUserError(List<JialiUsSyncLog> jialiUsSyncLogList,
                                                    UsDepartmentUser updateUsDepartmentUser, Exception e) {
        JialiUsSyncLog jialiUsSyncLog = new JialiUsSyncLog();
        jialiUsSyncLog.setUserId(updateUsDepartmentUser.getTartgetUserid());
        jialiUsSyncLog.setErrorType(JiaLiErrorSyncEnum.USER_DEPT_UPDATE.getCode());
        jialiUsSyncLog.setErrorDesc(e.getMessage());
        jialiUsSyncLog.setErrorJson(JSON.toJSONString(updateUsDepartmentUser));
        jialiUsSyncLog.setCreateTime(DateUtil.date());
        jialiUsSyncLog.setUpdateTime(DateUtil.date());
        jialiUsSyncLogList.add(jialiUsSyncLog);
    }

    /**
     * 判断需要修改的员工(修改,禁用,删除三种状态)
     *
     * @param dbUserMap
     * @param updateUsUserList
     * @param updateUsDepartmentUserList
     */
    private void getNeedUpdateUserList(Map<String, UsUser> dbUserMap, List<UsUser> updateUsUserList,
                                       List<UsDepartmentUser> updateUsDepartmentUserList,
                                       WechatStuffDTO wechatStuffDTO) {
        // 获取员工修改
        log.info("开始判断员工是否需要修改，员工AadId：{}", wechatStuffDTO.getAadId());
        log.info("判断员工修改-嘉里员工信息：{}", JSON.toJSONString(wechatStuffDTO));

        // 比对修改的
        UsUser dbUser = dbUserMap.get(wechatStuffDTO.getAadId());
        log.info("判断员工修改-数据库员工信息：{}", JSON.toJSONString(dbUser));

        String staffEnglishName = null;// 别名getAlias
        String englishTitle = null; // 职务getPosition
        String emailAddressKpl = null;// email getEmail
        String businessPhone = null; // 座机getTelephone
        String mobileNo = null; // 手机号
        String supervisorAadId = null; // 上级领导aad_id

        // 检查别名
        if (StringUtils.isNotBlank(wechatStuffDTO.getStaffEnglishName()) && !wechatStuffDTO.getStaffEnglishName()
                                                                                           .isEmpty() &&
                wechatStuffDTO.getStaffEnglishName()
                              .length() <= 64) {
            // 检查名称长度是否在 1 到 64 个字符之间
            staffEnglishName = wechatStuffDTO.getStaffEnglishName();
            log.info("判断员工修改-员工英文名称合法：{}", staffEnglishName);
        } else {
            log.info("判断员工修改-员工英文名称不合法：{}", wechatStuffDTO.getStaffEnglishName());
        }

        // 检查职务
        if (StringUtils.isNotBlank(wechatStuffDTO.getEnglishTitle()) && !wechatStuffDTO.getEnglishTitle()
                                                                                       .isEmpty() &&
                wechatStuffDTO.getEnglishTitle()
                              .length() <= 128) {
            // 检查名称长度是否在 1 到 128 个字符之间
            // 成员名称长度合法
            englishTitle = wechatStuffDTO.getEnglishTitle();
            log.info("判断员工修改-员工英文职务合法：{}", englishTitle);
        } else {
            log.info("判断员工修改-员工英文职务不合法：{}", wechatStuffDTO.getEnglishTitle());
        }

        // 检查邮箱
        if (StringUtils.isNotEmpty(wechatStuffDTO.getEmailAddressKpl()) && (wechatStuffDTO.getEmailAddressKpl()
                                                                                          .matches(
                                                                                                  "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"))) {
            emailAddressKpl = wechatStuffDTO.getEmailAddressKpl();
            log.info("判断员工修改-员工邮箱合法：{}", emailAddressKpl);
        } else {
            log.info("判断员工修改-员工邮箱不合法：{}", wechatStuffDTO.getEmailAddressKpl());
        }

        // 判断座机号码
        if (StringUtils.isNotEmpty(wechatStuffDTO.getBusinessPhone()) && wechatStuffDTO.getBusinessPhone()
                                                                                       .matches("^[0-9+,-]{1,32}$")) {
            businessPhone = wechatStuffDTO.getBusinessPhone();
            log.info("判断员工修改-员工座机号码合法：{}", businessPhone);
        } else {
            log.info("判断员工修改-员工座机号码不合法：{}", wechatStuffDTO.getBusinessPhone());
        }

        // 检查手机号
        if (StringUtils.isNotEmpty(wechatStuffDTO.getMobilePhone())) {
            mobileNo = wechatStuffDTO.getMobilePhone();
            log.info("判断员工修改-员工手机号码：{}", mobileNo);
        } else {
            log.info("判断员工修改-员工手机号码为空");
        }

        // 判断上级领导是否已经同步企微ir
        UsUser usUserDirectLeader = dbUserMap.get(wechatStuffDTO.getSupervisorAadId());
        if (Objects.nonNull(usUserDirectLeader) &&
                (((Objects.equals(usUserDirectLeader.getDataStatus(), DataStatusEnum.INSERT.getCode()) &&
                        Objects.equals(usUserDirectLeader.getSyncStatus(), SyncStatusEnum.SUCCESS.getCode()))) ||
                        (Objects.equals(usUserDirectLeader.getDataStatus(), DataStatusEnum.UPDATE.getCode())))) {
            supervisorAadId = wechatStuffDTO.getSupervisorAadId();
            log.info("判断员工修改-员工上级领导已同步，AadId：{}", supervisorAadId);
        } else {
            log.info("判断员工修改-员工上级领导未同步或不存在，AadId：{}", wechatStuffDTO.getSupervisorAadId());
        }

        // 部门比对
        // 企微部门ID换嘉里部门ID
        log.info("判断员工修改-开始查询员工部门关系");
        LambdaQueryWrapper<UsDepartmentUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UsDepartmentUser::getTargetDeptId, dbUser.getMainDepartment()
                                                                 .toString())
                    .eq(UsDepartmentUser::getAadId, dbUser.getAadId())
                    .eq(UsDepartmentUser::getTartgetUserid, dbUser.getSourceUserid())
                    .eq(UsDepartmentUser::getDeleted, DeleteStatusEnum.NOT_DELETED.getCode());
        List<UsDepartmentUser> usDepartmentUsers = usDepartmentUserService.list(queryWrapper);
        if (CollectionUtils.isEmpty(usDepartmentUsers)) {
            log.warn("判断员工修改-查询员工部门关系为空，无法继续处理");
            return;
        }
        UsDepartmentUser usDepartmentUser = usDepartmentUsers.get(0);
        log.info("判断员工修改-查询到员工部门关系：{}", JSON.toJSONString(usDepartmentUser));

        // 比对部门ID
        if (Objects.nonNull(usDepartmentUser) &&
                !Objects.equals(wechatStuffDTO.getDepartment(), usDepartmentUser.getSourceDeptId())) {
            log.info("判断员工修改-员工部门发生变更，原部门：{}，新部门：{}", usDepartmentUser.getSourceDeptId(),
                     wechatStuffDTO.getDepartment());
            dbUser = getUpdatedUsUser(wechatStuffDTO, dbUser, dbUserMap);
            // 部门员工信息修改
            UsDepartmentUser updateUsDepartmentUser = getUsDepartmentUser(wechatStuffDTO, usDepartmentUser);
            updateUsDepartmentUserList.add(updateUsDepartmentUser);
            updateUsUserList.add(dbUser);
            log.info("判断员工修改-员工部门变更，添加到更新列表");
            return;
        }

        // 逐个字段比对是否修改
        boolean needUpdate = false;
        StringBuilder updateReasons = new StringBuilder();

        if (!Objects.equals(wechatStuffDTO.getStaffChineseName(), dbUser.getName())) {
            needUpdate = true;
            updateReasons.append("中文名称变更(")
                         .append(dbUser.getName())
                         .append("->")
                         .append(wechatStuffDTO.getStaffChineseName())
                         .append("); ");
        }

        if (!Objects.equals(staffEnglishName, dbUser.getAlias())) {
            needUpdate = true;
            updateReasons.append("英文名称变更(")
                         .append(dbUser.getAlias())
                         .append("->")
                         .append(staffEnglishName)
                         .append("); ");
        }

        if (!Objects.equals(englishTitle, dbUser.getPosition())) {
            needUpdate = true;
            updateReasons.append("职位变更(")
                         .append(dbUser.getPosition())
                         .append("->")
                         .append(englishTitle)
                         .append("); ");
        }

        if (!Objects.equals(GenderEnum.getGender(wechatStuffDTO.getGender()), dbUser.getGender())) {
            needUpdate = true;
            updateReasons.append("性别变更(")
                         .append(dbUser.getGender())
                         .append("->")
                         .append(GenderEnum.getGender(wechatStuffDTO.getGender()))
                         .append("); ");
        }

        if (!Objects.equals(emailAddressKpl, dbUser.getEmail())) {
            needUpdate = true;
            updateReasons.append("邮箱变更(")
                         .append(dbUser.getEmail())
                         .append("->")
                         .append(emailAddressKpl)
                         .append("); ");
        }

        if (!Objects.equals(businessPhone, dbUser.getTelephone())) {
            needUpdate = true;
            updateReasons.append("座机变更(")
                         .append(dbUser.getTelephone())
                         .append("->")
                         .append(businessPhone)
                         .append("); ");
        }

        if (!Objects.equals(supervisorAadId, dbUser.getDirectLeader())) {
            needUpdate = true;
            updateReasons.append("上级变更(")
                         .append(dbUser.getDirectLeader())
                         .append("->")
                         .append(supervisorAadId)
                         .append("); ");
        }

        if (StringUtils.isNotEmpty(mobileNo) && StringUtils.isEmpty(dbUser.getMobile())) {
            needUpdate = true;
            updateReasons.append("手机号变更(")
                         .append(dbUser.getMobile())
                         .append("->")
                         .append(mobileNo)
                         .append("); ");
        }

        // 自定义字段 解析json获取对外职务 对比
        log.info("判断员工修改-开始检查员工自定义字段");
        String extattr = dbUser.getExtattr();
        Extattr dbExtattr = JSON.parseObject(extattr, Extattr.class);
        if (Objects.nonNull(dbExtattr) && CollectionUtils.isNotEmpty(dbExtattr.getAttrs())) {
            String dbChineseTitle = dbExtattr.getAttrs()
                                             .get(0)
                                             .getText()
                                             .getValue();
            if (!Objects.equals(wechatStuffDTO.getChineseTitle(), dbChineseTitle)) {
                log.info("判断员工修改-员工中文职务发生变更，原职务：{}，新职务：{}", dbChineseTitle,
                         wechatStuffDTO.getChineseTitle());
                needUpdate = true;
                updateReasons.append("中文职务变更(")
                             .append(dbChineseTitle)
                             .append("->")
                             .append(wechatStuffDTO.getChineseTitle())
                             .append("); ");
                log.info("判断员工修改-员工中文职务变更，添加到更新列表");
            } else {
                log.info("判断员工修改-员工中文职务未变更：{}", dbChineseTitle);
            }
        } else {
            log.info("判断员工修改-员工自定义字段为空或不包含属性");
        }

        if (compareEmployeeStatus(dbUser, wechatStuffDTO, updateReasons)) {
            needUpdate = true;
        }

        if (needUpdate) {
            log.info("判断员工修改-员工基本信息发生变更，变更原因：{}", updateReasons);
            getUpdatedUsUser(wechatStuffDTO, dbUser, dbUserMap);
            updateUsUserList.add(dbUser);
            log.info("判断员工修改-员工基本信息变更，添加到更新列表");
        } else {
            log.info("判断员工修改-员工基本信息未发生变更");
        }
        log.info("判断员工修改-员工信息处理完成");
    }

    /**
     * 比较员工状态是否发生变更
     *
     * @param dbUser 数据库中的用户信息
     * @param wechatStuffDTO 微信员工DTO
     * @param updateReasons 更新原因
     * @return 如果状态发生变更返回true，否则返回false
     */
    private boolean compareEmployeeStatus(UsUser dbUser, WechatStuffDTO wechatStuffDTO, StringBuilder updateReasons) {
        if (!Objects.equals(dbUser.getEnable(), wechatStuffDTO.getEnabled())) {
            updateReasons.append("员工状态变更(")
                        .append("原状态：")
                        .append(dbUser.getEnable())
                        .append("，新状态：")
                        .append(wechatStuffDTO.getEnabled())
                        .append("); ");
            return true;
        }
        return false;
    }

    private UsDepartmentUser getUsDepartmentUser(WechatStuffDTO wechatStuffDTO,
                                                 UsDepartmentUser updateUsDepartmentUser) {
        log.info("获取部门员工关系-开始获取部门员工关系，员工AadId：{}，部门ID：{}", wechatStuffDTO.getAadId(),
                 wechatStuffDTO.getDepartment());
        List<UsDepartment> usDepartments = usDepartmentService.lambdaQuery()
                                                              .eq(UsDepartment::getSourceDeptId,
                                                                  wechatStuffDTO.getDepartment())
                                                              .eq(UsDepartment::getDeleted,
                                                                  DeleteStatusEnum.NOT_DELETED.getCode())
                                                              .list();
        log.info("获取部门员工关系-查询到部门信息数量：{}", usDepartments.size());

        if (CollectionUtils.isEmpty(usDepartments)) {
            String content = String.format("get us department user us departments empty, jia li user: %s",
                                           JSONObject.toJSONString(wechatStuffDTO));
            log.warn("获取部门员工关系-未找到对应部门信息，员工信息：{}", JSON.toJSONString(wechatStuffDTO));
            sendErrorEmail(content);
            return updateUsDepartmentUser;
        }

        if (usDepartments.size() > 1) {
            log.warn("获取部门员工关系-发现重复的部门信息：{}", JSON.toJSONString(usDepartments));
        }

        UsDepartment usDepartment = usDepartments.get(0);
        log.info("获取部门员工关系-使用部门信息：{}", JSON.toJSONString(usDepartment));

        log.info("获取部门员工关系-更新部门员工关系前：{}", JSON.toJSONString(updateUsDepartmentUser));
        updateUsDepartmentUser.setAadId(wechatStuffDTO.getAadId());
        updateUsDepartmentUser.setSourceDeptId(wechatStuffDTO.getDepartment());
        updateUsDepartmentUser.setTargetDeptId(usDepartment.getTargetDeptId());
        updateUsDepartmentUser.setTartgetUserid(wechatStuffDTO.getAdUsername());
        updateUsDepartmentUser.setGmtModified(DateUtil.date());
        log.info("获取部门员工关系-更新部门员工关系后：{}", JSON.toJSONString(updateUsDepartmentUser));

        return updateUsDepartmentUser;
    }

    private UsUser getUpdatedUsUser(WechatStuffDTO wechatStuffDTO, UsUser usUser, Map<String, UsUser> dbUserMap) {
        log.info("更新员工信息-开始更新员工信息，员工AadId：{}", wechatStuffDTO.getAadId());
        log.info("更新员工信息-嘉里员工信息:{}", JSON.toJSONString(wechatStuffDTO));
        log.info("更新员工信息-数据库员工信息:{}", JSON.toJSONString(usUser));

        String staffChineseName = wechatStuffDTO.getStaffChineseName();
        String staffEnglishName = wechatStuffDTO.getStaffEnglishName();
        String englishTitle = wechatStuffDTO.getEnglishTitle();
        String emailAddressKpl = wechatStuffDTO.getEmailAddressKpl();
        String businessPhone = wechatStuffDTO.getBusinessPhone();
        String supervisorAdUsername = wechatStuffDTO.getSupervisorAdUsername();
        String supervisorAadId = wechatStuffDTO.getSupervisorAadId();
        log.info(
                "更新员工信息-获取员工基本信息：中文名={}, 英文名={}, 职务={}, 邮箱={}, 座机={}, 上级用户名={}, 上级AadId={}",
                staffChineseName, staffEnglishName, englishTitle, emailAddressKpl, businessPhone, supervisorAdUsername,
                supervisorAadId);

        usUser.setSourceUserid(wechatStuffDTO.getAdUsername());
        usUser.setName(wechatStuffDTO.getStaffChineseName());
        log.info("更新员工信息-设置员工源用户ID和名称：{}, {}", wechatStuffDTO.getAdUsername(),
                 wechatStuffDTO.getStaffChineseName());

        // 检查直属上司
        UsUser leader = dbUserMap.get(supervisorAadId);
        if (Objects.nonNull(leader)) {
            usUser.setDirectLeader(supervisorAadId);
            log.info("更新员工信息-设置员工直属上级：{}", supervisorAadId);
        } else {
            log.info("更新员工信息-未找到员工直属上级：{}", supervisorAadId);
        }

        // 检查别名
        if (StringUtils.isNotBlank(staffEnglishName) && !staffEnglishName.isEmpty() &&
                staffEnglishName.length() <= 64) {
            // 检查名称长度是否在 1 到 64 个字符之间
            // 成员名称长度合法
            usUser.setAlias(staffEnglishName);
            log.info("更新员工信息-设置员工别名：{}", staffEnglishName);
        } else {
            usUser.setAlias(null);
            log.info("更新员工信息-员工别名不合法，设置为null");
        }

        // 检查职务
        if (StringUtils.isNotBlank(englishTitle) && !englishTitle.isEmpty() && englishTitle.length() <= 128) {
            // 检查名称长度是否在 1 到 128 个字符之间
            // 成员名称长度合法
            usUser.setPosition(englishTitle);
            log.info("更新员工信息-设置员工职务：{}", englishTitle);
        } else {
            usUser.setPosition(null);
            log.info("更新员工信息-员工职务不合法，设置为null");
        }

        // 性别
        usUser.setGender(GenderEnum.getGender(wechatStuffDTO.getGender()));
        log.info("更新员工信息-设置员工性别：{}", GenderEnum.getGender(wechatStuffDTO.getGender()));

        // 检查邮箱
        if (StringUtils.isNotEmpty(emailAddressKpl)) {
            String tempEmail = emailAddressKpl.trim();
            if (tempEmail.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$") &&
                    !"<EMAIL>".equalsIgnoreCase(tempEmail)) {
                usUser.setEmail(wechatStuffDTO.getEmailAddressKpl());
                log.info("更新员工信息-设置员工邮箱：{}", wechatStuffDTO.getEmailAddressKpl());
            } else {
                usUser.setEmail(null);
                log.info("更新员工信息-员工邮箱不合法，设置为null：{}", tempEmail);
            }
        }

        // 判断座机号码
        if (StringUtils.isNotBlank(businessPhone) && businessPhone.matches("^[0-9+,-]{1,32}$")) {
            // 座机号码格式正确
            usUser.setTelephone(businessPhone);
            log.info("更新员工信息-设置员工座机：{}", businessPhone);
        } else {
            // 座机号码格式不正确
            usUser.setTelephone(null);
            log.info("更新员工信息-员工座机不合法，设置为null：{}", businessPhone);
        }

        // 判断手机号码
        if (StringUtils.isNotBlank(wechatStuffDTO.getMobilePhone()) && StringUtils.isEmpty(usUser.getMobile())) {
            usUser.setMobile(wechatStuffDTO.getMobilePhone());
            log.info("更新员工信息-设置员工手机号：{}", wechatStuffDTO.getMobilePhone());
        } else {
            log.info("更新员工信息-不更新员工手机号");
        }

        // 判断上级领导是否已经同步企微//判断企微是否存在
        UsUser usUserDirectLeader = dbUserMap.get(wechatStuffDTO.getSupervisorAadId());
        if (Objects.nonNull(usUserDirectLeader) &&
                (((Objects.equals(usUserDirectLeader.getDataStatus(), DataStatusEnum.INSERT.getCode()) &&
                        Objects.equals(usUserDirectLeader.getSyncStatus(), SyncStatusEnum.SUCCESS.getCode()))) ||
                        (Objects.equals(usUserDirectLeader.getDataStatus(), DataStatusEnum.UPDATE.getCode())))) {
            usUser.setDirectLeader(wechatStuffDTO.getSupervisorAadId());
            log.info("更新员工信息-设置员工直属上级(已同步)：{}", wechatStuffDTO.getSupervisorAadId());
        } else {
            usUser.setDirectLeader(null);
            log.info("更新员工信息-员工直属上级未同步或不存在，设置为null：{}", wechatStuffDTO.getSupervisorAadId());
        }

        usUser.setEnable(UserEnableEnum.ENABLE.getCode());
        log.info("更新员工信息-设置员工状态为启用：{}", UserEnableEnum.ENABLE.getCode());

        // 封装-自定义字段
        Extattr extattr = getExtattr(wechatStuffDTO);
        // 自定义字段
        usUser.setExtattr(JSON.toJSONString(extattr));
        log.info("更新员工信息-设置员工自定义字段：{}", JSON.toJSONString(extattr));

        log.info("更新员工信息-不更新员工对外属性");

        List<UsDepartment> usDepartments = usDepartmentService.lambdaQuery()
                                                              .eq(UsDepartment::getSourceDeptId,
                                                                  wechatStuffDTO.getDepartment())
                                                              .eq(UsDepartment::getDeleted,
                                                                  DeleteStatusEnum.NOT_DELETED.getCode())
                                                              .list();
        log.info("更新员工信息-查询员工部门信息，数量：{}", usDepartments.size());

        if (CollectionUtils.isEmpty(usDepartments)) {
            String content =
                    String.format("update user departments empty user: %s", JSONObject.toJSONString(wechatStuffDTO));
            log.warn("更新员工信息-未找到对应部门信息，员工信息：{}", JSON.toJSONString(wechatStuffDTO));
            sendErrorEmail(content);
            return usUser;
        }

        if (usDepartments.size() > 1) {
            log.warn("更新员工信息-发现重复的部门信息：{}", JSON.toJSONString(usDepartments));
        }

        UsDepartment usDepartment = usDepartments.get(0);
        if (Objects.isNull(usDepartment) || StringUtils.isBlank(usDepartment.getTargetDeptId())) {
            String content = String.format("update user dept id_empty us departments: %s",
                                           JSONObject.toJSONString(usDepartments));
            log.warn("更新员工信息-部门目标ID为空，部门信息：{}", JSON.toJSONString(usDepartments));
            sendErrorEmail(content);
            return usUser;
        }
        log.info("更新员工信息-当前嘉里员工对应的部门:{}", JSON.toJSONString(usDepartment));

        usUser.setMainDepartment(Integer.valueOf(usDepartment.getTargetDeptId()));
        log.info("更新员工信息-设置员工主部门：{}", usDepartment.getTargetDeptId());

        if (Objects.equals(usUser.getDataStatus(), DataStatusEnum.INSERT.getCode()) &&
                Objects.equals(usUser.getSyncStatus(), SyncStatusEnum.FAIL.getCode())) {
            usUser.setDataStatus(DataStatusEnum.INSERT.getCode());
            log.info("更新员工信息-员工为新增失败状态，保持数据状态为新增：{}", DataStatusEnum.INSERT.getCode());
        } else {
            usUser.setDataStatus(DataStatusEnum.UPDATE.getCode());
            log.info("更新员工信息-设置数据状态为更新：{}", DataStatusEnum.UPDATE.getCode());
        }
        usUser.setEnable(wechatStuffDTO.getEnabled());
        usUser.setSyncStatus(DataStatusEnum.INIT.getCode());
        usUser.setGmtModified(DateUtil.date());
        usUser.setTargetUserid(wechatStuffDTO.getAdUsername());
        usUser.setAadId(wechatStuffDTO.getAadId());
        usUser.setGender(GenderEnum.getGender(wechatStuffDTO.getGender()));
        log.info("更新员工信息-设置同步状态为初始化：{}，更新修改时间：{}，目标用户ID：{}，AadId：{}",
                 DataStatusEnum.INIT.getCode(), DateUtil.date(), wechatStuffDTO.getAdUsername(),
                 wechatStuffDTO.getAadId());

        log.info("更新员工信息-员工信息更新完成，员工AadId：{}", wechatStuffDTO.getAadId());
        return usUser;
    }

    /**
     * 员工新增
     *
     * @param addUsUserList
     * @param addUsDepartmentUserList
     * @param wechatStuffDTO
     */
    private void getAddUserList(List<UsUser> addUsUserList, List<UsDepartmentUser> addUsDepartmentUserList,
                                WechatStuffDTO wechatStuffDTO, Map<String, UsDepartment> usDepartmentMap) {
        log.info("新增员工-开始处理员工新增，员工AadId：{}", wechatStuffDTO.getAadId());

        String staffChineseName = wechatStuffDTO.getStaffChineseName();
        String staffEnglishName = wechatStuffDTO.getStaffEnglishName();
        String englishTitle = wechatStuffDTO.getEnglishTitle();
        String emailAddressKpl = wechatStuffDTO.getEmailAddressKpl();
        String businessPhone = wechatStuffDTO.getBusinessPhone();
        log.info("新增员工-获取员工基本信息：中文名={}, 英文名={}, 职务={}, 邮箱={}, 座机={}", staffChineseName,
                 staffEnglishName, englishTitle, emailAddressKpl, businessPhone);

        // 员工同步新增
        UsUser addUsUser = new UsUser();
        addUsUser.setTenantId(jiaLiConfig.getTenantId());
        addUsUser.setSourceUserid(wechatStuffDTO.getAdUsername());
        addUsUser.setName(staffChineseName);
        log.info("新增员工-设置租户ID：{}，源用户ID：{}，名称：{}", jiaLiConfig.getTenantId(),
                 wechatStuffDTO.getAdUsername(), staffChineseName);

        // 检查别名
        if (staffEnglishName != null && !staffEnglishName.isEmpty()) {
            // 检查名称长度是否在 1 到 64 个字符之间
            if (staffEnglishName.length() <= 64) {
                // 成员名称长度合法
                addUsUser.setAlias(staffEnglishName);
                log.info("新增员工-设置员工别名：{}", staffEnglishName);
            } else {
                addUsUser.setAlias(null);
                log.info("新增员工-员工别名长度超过限制，设置为null：{}", staffEnglishName);
            }
        } else {
            log.info("新增员工-员工别名为空");
        }

        // 检查手机号 //更新不同步
        if (StringUtils.isNotBlank(wechatStuffDTO.getMobilePhone())) {
            log.info("新增员工-员工手机号不为空，但不设置：{}", wechatStuffDTO.getMobilePhone());
            addUsUser.setMobile(null);
        } else {
            addUsUser.setMobile(wechatStuffDTO.getMobilePhone());
            log.info("新增员工-设置员工手机号：{}", wechatStuffDTO.getMobilePhone());
        }

        // 检查职务
        if (StringUtils.isNotBlank(englishTitle) && !englishTitle.isEmpty() && englishTitle.length() <= 128) {
            // 检查名称长度是否在 1 到 128 个字符之间
            // 成员名称长度合法
            addUsUser.setPosition(englishTitle);
            log.info("新增员工-设置员工职务：{}", englishTitle);
        } else {
            addUsUser.setPosition(null);
            log.info("新增员工-员工职务不合法，设置为null：{}", englishTitle);
        }

        // 性别
        addUsUser.setGender(GenderEnum.getGender(wechatStuffDTO.getGender()));
        log.info("新增员工-设置员工性别：{}", GenderEnum.getGender(wechatStuffDTO.getGender()));

        // 检查邮箱
        if (StringUtils.isNotBlank(emailAddressKpl)) {
            String tempEmail = emailAddressKpl.trim();
            if (tempEmail.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$") &&
                    !"<EMAIL>".equalsIgnoreCase(tempEmail)) {
                addUsUser.setEmail(wechatStuffDTO.getEmailAddressKpl());
                log.info("新增员工-设置员工邮箱：{}", wechatStuffDTO.getEmailAddressKpl());
            } else {
                addUsUser.setEmail(null);
                log.info("新增员工-员工邮箱不合法，设置为null：{}", tempEmail);
            }
        } else {
            log.info("新增员工-员工邮箱为空");
        }

        // 判断座机号码
        if (StringUtils.isNotBlank(businessPhone)) {
            if (businessPhone.matches("^[0-9+,-]{1,32}$")) {
                // 座机号码格式正确
                addUsUser.setTelephone(businessPhone);
                log.info("新增员工-设置员工座机：{}", businessPhone);
            } else {
                // 座机号码格式不正确
                addUsUser.setTelephone(null);
                log.info("新增员工-员工座机不合法，设置为null：{}", businessPhone);
            }
        } else {
            addUsUser.setTelephone(null);
            log.info("新增员工-员工座机为空，设置为null");
        }

        if (!wechatStuffDTO.isEnabled()) {
            log.warn("新增员工-员工状态为禁用，设置为禁用");
        }
        addUsUser.setEnable(wechatStuffDTO.isEnabled() ? 1 : 0);

        // 封装-自定义字段
        if (StringUtils.isNoneEmpty(wechatStuffDTO.getChineseTitle())) {
            Extattr extattr = getExtattr(wechatStuffDTO);
            // 自定义字段
            addUsUser.setExtattr(JSON.toJSONString(extattr));
            log.info("新增员工-设置员工自定义字段：{}", JSON.toJSONString(extattr));
        } else {
            log.info("新增员工-员工中文职务为空，不设置自定义字段");
        }

        // 对外职务.
        addUsUser.setExternalPosition(jiaLiConfig.getExternalPosition());// 更新忽略
        log.info("新增员工-设置员工对外职务：{}", jiaLiConfig.getExternalPosition());

        // 部门ID
        UsDepartment usDepartment = usDepartmentMap.get(wechatStuffDTO.getDepartment());
        log.info("新增员工-获取员工部门信息：{}", JSON.toJSONString(usDepartment));

        if (Objects.isNull(usDepartment) || StringUtils.isBlank(usDepartment.getTargetDeptId())) {
            String content = String.format("add user dept id_empty user:%s us_department: %s",
                                           JSONObject.toJSONString(wechatStuffDTO),
                                           JSONObject.toJSONString(usDepartment));
            log.warn("新增员工-未找到对应部门信息或部门目标ID为空，员工信息：{}, 部门信息：{}",
                     JSON.toJSONString(wechatStuffDTO), JSON.toJSONString(usDepartment));
            sendErrorEmail(content);
            return;
        }

        if (Objects.equals(usDepartment.getDeleted(), DeleteStatusEnum.DELETED.getCode())) {
            log.warn("新增员工-员工所在部门已被删除，部门ID：{}", usDepartment.getSourceDeptId());
        }

        addUsUser.setMainDepartment(Integer.valueOf(usDepartment.getTargetDeptId()));
        log.info("新增员工-设置员工主部门：{}", usDepartment.getTargetDeptId());

        addUsUser.setDataStatus(DataStatusEnum.INSERT.getCode());
        addUsUser.setSyncStatus(DataStatusEnum.INIT.getCode());
        addUsUser.setGmtCreate(DateUtil.date());
        addUsUser.setGmtModified(DateUtil.date());
        addUsUser.setTargetUserid(wechatStuffDTO.getAdUsername());
        addUsUser.setAadId(wechatStuffDTO.getAadId());
        log.info("新增员工-设置数据状态为新增：{}，同步状态为初始化：{}，创建时间：{}，修改时间：{}，目标用户ID：{}，AadId：{}",
                 DataStatusEnum.INSERT.getCode(), DataStatusEnum.INIT.getCode(), DateUtil.date(), DateUtil.date(),
                 wechatStuffDTO.getAdUsername(), wechatStuffDTO.getAadId());

        addUsUser.setToInvite(ToInviteEnum.FALSE.getCode());
        log.info("新增员工-设置是否邀请为否：{}", ToInviteEnum.FALSE.getCode());

        addUsUserList.add(addUsUser);
        log.info("新增员工-添加员工到新增列表");

        // 部门员工关系同步新增
        UsDepartmentUser addUsDepartmentUser = new UsDepartmentUser();
        addUsDepartmentUser.setTenantId(jiaLiConfig.getTenantId());
        addUsDepartmentUser.setAadId(wechatStuffDTO.getAadId());
        addUsDepartmentUser.setSourceDeptId(wechatStuffDTO.getDepartment());
        addUsDepartmentUser.setTargetDeptId(usDepartment.getTargetDeptId());
        addUsDepartmentUser.setTartgetUserid(wechatStuffDTO.getAdUsername());
        addUsDepartmentUser.setGmtCreate(DateUtil.date());
        addUsDepartmentUser.setGmtModified(DateUtil.date());
        log.info(
                "新增员工-创建部门员工关系：租户ID={}，AadId={}，源部门ID={}，目标部门ID={}，目标用户ID={}，创建时间={}，修改时间={}",
                jiaLiConfig.getTenantId(), wechatStuffDTO.getAadId(), wechatStuffDTO.getDepartment(),
                usDepartment.getTargetDeptId(), wechatStuffDTO.getAdUsername(), DateUtil.date(), DateUtil.date());

        addUsDepartmentUserList.add(addUsDepartmentUser);
        log.info("新增员工-添加部门员工关系到新增列表");

        log.info("新增员工-员工新增处理完成，员工AadId：{}", wechatStuffDTO.getAadId());
    }

    protected void sendErrorEmail(String errorMessage) {
        log.info("发送错误邮件-开始发送错误邮件，错误信息：{}", errorMessage);
        EmailSendCommandVo command = new EmailSendCommandVo();
        command.setSendTos(emailSendToList);
        command.setSubject("["+environment.getProperty("spring.profiles.active")+"] wshot-ka-jiali-service");
        command.setText(generateEmailContent(errorMessage));
        command.setHtml(true);
        log.info("发送错误邮件-邮件内容准备完成，收件人：{}", JSON.toJSONString(emailSendToList));

        try {
            log.info("发送错误邮件-开始发送邮件");
            String resp = messageClient.sendWithReply(command);
            log.info("发送错误邮件-邮件发送完成，响应: {}", JSONUtil.toJsonStr(resp));
        } catch (Exception e) {
            log.error("发送错误邮件-邮件发送失败，异常信息", e);
        }
    }

    private String generateEmailContent(String errorMessage) {
        log.info("生成邮件内容-开始生成邮件内容，错误信息：{}", errorMessage);
        Context context = new Context();
        context.setVariable("executionResult", "同步失败");
        context.setVariable("errorHappeningTime", ZonedDateTime.now()
                                                               .toString());
        context.setVariable("errorMessage", errorMessage);
        String content = templateEngine.process("exceptionNotificationMessage", context);
        log.info("生成邮件内容-邮件内容生成完成，长度：{}", content.length());
        return content;
    }

    /**
     * 封装自定义数据
     *
     * @param jiaLiUser
     * @return
     */
    private Extattr getExtattr(WechatStuffDTO jiaLiUser) {
        log.info("获取自定义数据-开始获取自定义数据，员工信息：{}", JSON.toJSONString(jiaLiUser));
        Text textDTO = new Text();
        textDTO.setValue(jiaLiUser.getChineseTitle());
        log.info("获取自定义数据-设置文本值：{}", jiaLiUser.getChineseTitle());

        List<Attrs> attrsDTOList = new ArrayList<>();
        Attrs attrsDTO = new Attrs();
        attrsDTO.setType(TYPE);
        attrsDTO.setName(jiaLiConfig.getExtattrName());
        attrsDTO.setText(textDTO);
        log.info("获取自定义数据-创建属性：类型={}，名称={}，文本值={}", TYPE, jiaLiConfig.getExtattrName(),
                 jiaLiUser.getChineseTitle());

        attrsDTOList.add(attrsDTO);
        log.info("获取自定义数据-添加属性到列表");

        Extattr extattr = new Extattr();
        extattr.setAttrs(attrsDTOList);
        log.info("获取自定义数据-设置属性列表到自定义数据");

        log.info("获取自定义数据-自定义数据获取完成");
        return extattr;
    }

}