package com.wshoto.jiali.service.impl;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.wshoto.jiali.service.DbManagerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * Implementation of the Database Manager Service.
 * Handles database management operations using dynamic datasource.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DbManagerServiceImpl implements DbManagerService {

    public static final Set<String> RESERVED_KEYWORDS =
            Set.of("order", "user", "group", "table", "index", "column", "constraint", "select", "from", "where", "and",
                   "or", "having", "limit", "offset", "case", "when", "then", "else", "end");

    private final DataSource dataSource;

    private final JdbcTemplate jdbcTemplate;

    // 常量定义
    private static final String KEY_COLUMNS = "columns";

    private static final String KEY_MESSAGE = "message";

    private static final String KEY_ROWS_AFFECTED = "rowsAffected";

    private static final String KEY_SUCCESS = "success";

    private static final String KEY_DATA = "data";

    private static final String KEY_TOTAL = "total";

    private static final String KEY_PAGE = "page";

    private static final String KEY_SIZE = "size";

    private static final String KEY_ERROR = "error";

    private static final String KEY_TYPE = "type";

    private static final String TYPE_SELECT = "SELECT";

    private static final String TYPE_UPDATE = "UPDATE";

    private static final String KEY_ROW_COUNT = "rowCount";

    /**
     * Gets all available databases.
     *
     * @return list of database names
     */
    @Override
    public List<String> getDatabases() {
        var ds = (DynamicRoutingDataSource) dataSource;
        return new ArrayList<>(ds.getDataSources()
                                 .keySet());
    }

    /**
     * Gets all tables for a specific database.
     *
     * @param dbName the database name
     * @return list of table names
     */
    @Override
    public List<String> getTables(String dbName) {
        try {
            DynamicDataSourceContextHolder.push(dbName);

            // PostgreSQL query to get all tables
            String sql = "select table_name from information_schema.tables " +
                    "where table_schema = 'public' and table_type = 'BASE TABLE' " + "order by table_name";

            log.info("Fetching tables for database: {}", dbName);
            return Optional.ofNullable(jdbcTemplate.queryForList(sql, String.class))
                           .orElse(Collections.emptyList());
        } catch (Exception e) {
            log.error("Error getting tables for database: {}, error: {}", dbName, e.getMessage(), e);
            return Collections.emptyList();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
 * Gets table structure for a specific table including column comments.
     *
     * @param dbName    the database name
     * @param tableName the table name
 * @return table structure information with comments
     */
    @Override
    public List<Map<String, Object>> getTableStructure(String dbName, String tableName) {
        try {
            DynamicDataSourceContextHolder.push(dbName);

        // PostgreSQL query to get table structure with comments
            String sql =
                "SELECT " +
                "    c.column_name, " +
                "    c.data_type, " +
                "    c.is_nullable, " +
                "    c.column_default, " +
                "    pgd.description AS column_comment " +
                "FROM information_schema.columns c " +
                "LEFT JOIN pg_catalog.pg_description pgd " +
                "    ON pgd.objoid = (SELECT oid FROM pg_catalog.pg_class WHERE relname = c.table_name AND relnamespace = (SELECT oid FROM pg_catalog.pg_namespace WHERE nspname = 'public')) " +
                "    AND pgd.objsubid = c.ordinal_position " +
                "WHERE c.table_schema = 'public' " +
                "    AND c.table_name = ? " +
                "ORDER BY c.ordinal_position";

            return jdbcTemplate.queryForList(sql, tableName);
        } catch (Exception e) {
            log.error("Error getting structure for table: {} in database: {}", tableName, dbName, e);
            return Collections.emptyList();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * Gets table data with pagination.
     *
     * @param dbName    the database name
     * @param tableName the table name
     * @param page      the page number
     * @param size      the page size
     * @return paginated table data
     */
    @Override
    public Map<String, Object> getTableData(String dbName, String tableName, int page, int size) {
        try {
            DynamicDataSourceContextHolder.push(dbName);

            // Calculate offset
            int offset = (page - 1) * size;

            // Get total count
            String countSql = "SELECT COUNT(*) FROM " + tableName;
            Long total = jdbcTemplate.queryForObject(countSql, Long.class);

            // Get paginated data
            String dataSql = "SELECT * FROM " + tableName + " LIMIT " + size + " OFFSET " + offset;
            List<Map<String, Object>> data = jdbcTemplate.queryForList(dataSql);

            // Get column names
            String columnSql = "select column_name from information_schema.columns " +
                    "where table_schema = 'public' and table_name = ? " + "order by ordinal_position";
            List<String> columns = jdbcTemplate.queryForList(columnSql, String.class, tableName);

            // Prepare result
            Map<String, Object> result = new HashMap<>();
            result.put(KEY_TOTAL, total);
            result.put(KEY_DATA, data);
            result.put(KEY_COLUMNS, columns);
            result.put(KEY_PAGE, page);
            result.put(KEY_SIZE, size);

            return result;
        } catch (Exception e) {
            log.error("Error getting data for table: {} in database: {}", tableName, dbName, e);
            return Map.of(KEY_ERROR, true, KEY_MESSAGE, e.getMessage(), KEY_TOTAL, 0, KEY_DATA, Collections.emptyList(),
                          KEY_COLUMNS, Collections.emptyList(), KEY_PAGE, page, KEY_SIZE, size);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * Executes custom SQL query.
     *
     * @param dbName the database name
     * @param sql    the SQL query to execute
     * @return query results
     */
    @Override
    public Map<String, Object> executeSql(String dbName, String sql) {
        try {
            DynamicDataSourceContextHolder.push(dbName);

            // Check if it's a SELECT query
            String trimmedSql = sql.trim()
                                   .toLowerCase();
            boolean isSelect = trimmedSql.startsWith("select");

            Map<String, Object> result = new HashMap<>();

            if (isSelect) {
                // For SELECT queries, return the result set
                List<Map<String, Object>> data = jdbcTemplate.queryForList(sql);

                // Extract column names from the first row
                List<String> columns = data.isEmpty() ? Collections.emptyList() : new ArrayList<>(data.get(0)
                                                                                                      .keySet());

                result.put(KEY_DATA, data);
                result.put(KEY_COLUMNS, columns);
                result.put(KEY_ROW_COUNT, data.size());
                result.put(KEY_TYPE, TYPE_SELECT);
            } else {
                // For non-SELECT queries, return the affected row count
                int rowsAffected = jdbcTemplate.update(sql);
                result.put(KEY_ROWS_AFFECTED, rowsAffected);
                result.put(KEY_TYPE, TYPE_UPDATE);
            }

            result.put(KEY_SUCCESS, true);
            return result;
        } catch (Exception e) {
            log.error("Error executing SQL in database: {}, SQL: {}", dbName, sql, e);
            return Map.of(KEY_SUCCESS, false, KEY_ERROR, true, KEY_MESSAGE, e.getMessage());
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    private String quoteIdentifier(String identifier) {
        if (RESERVED_KEYWORDS.contains(identifier.toLowerCase())) {
            return "\"" + identifier + "\"";
        }
        return identifier;
    }

    /**
     * Updates table data.
     *
     * @param dbName    the database name
     * @param tableName the table name
     * @param id        the record id
     * @param data      the updated data
     * @return operation result
     */
    public Map<String, Object> updateData(String dbName, String tableName, String id, Map<String, Object> data) {
        try {
            DynamicDataSourceContextHolder.push(dbName);

            // Get primary key column and type
            String pkColumn = getPrimaryKeyColumn(dbName, tableName);

            if (!StringUtils.hasText(pkColumn)) {
                return Map.of(KEY_SUCCESS, false, KEY_MESSAGE, "无法确定表的主键列");
            }

            // Build SET clause
            StringBuilder setClause = new StringBuilder();
            List<Object> params = new ArrayList<>();

            data.forEach((column, value) -> {
                if (!setClause.isEmpty()) {
                    setClause.append(", ");
                }

                String columnName = quoteIdentifier(column);

                // 处理不同的列类型
                if (value == null) {
                    setClause.append(columnName)
                             .append(" = NULL");
                } else if (column.equals("gmt_create") || column.equals("gmt_modified") || column.endsWith("_time")) {
                    setClause.append(columnName)
                             .append(" = ?::timestamp");
                    params.add(value);
                } else if ((column.equals("deleted") || column.equals("version") || column.equals("data_status") ||
                        column.equals("sync_status")) && value instanceof String string && string.matches("\\d+")) {
                    // 只对数字字符串进行smallint转换
                    setClause.append(columnName)
                             .append(" = ?::smallint");
                    params.add(value);
                } else {
                    setClause.append(columnName)
                             .append(" = ?");
                    params.add(value);
                }
            });

            // Execute update with properly typed primary key parameter
            String sql = "UPDATE " + tableName + " SET " + setClause + " WHERE " + quoteIdentifier(pkColumn) +
                    " = ?::bigint";

            // Add WHERE clause parameter
            params.add(id);

            int rowsAffected = jdbcTemplate.update(sql, params.toArray());

            return Map.of(KEY_SUCCESS, true, KEY_ROWS_AFFECTED, rowsAffected);
        } catch (Exception e) {
            log.error("Error updating data in table: {} in database: {}", tableName, dbName, e);
            return Map.of(KEY_SUCCESS, false, KEY_MESSAGE, e.getMessage());
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * Deletes table data.
     *
     * @param dbName    the database name
     * @param tableName the table name
     * @param id        the record id
     * @return operation result
     */
    @Override
    public Map<String, Object> deleteData(String dbName, String tableName, String id) {
        try {
            DynamicDataSourceContextHolder.push(dbName);

            // Get primary key column
            String pkColumn = getPrimaryKeyColumn(dbName, tableName);

            if (!StringUtils.hasText(pkColumn)) {
                return Map.of(KEY_SUCCESS, false, KEY_MESSAGE, "无法确定表的主键列");
            }

            // Execute delete
            String sql = "DELETE FROM " + tableName + " WHERE " + pkColumn + " = ?";
            int rowsAffected = jdbcTemplate.update(sql, id);

            return Map.of(KEY_SUCCESS, true, KEY_ROWS_AFFECTED, rowsAffected);
        } catch (Exception e) {
            log.error("Error deleting data from table: {} in database: {}", tableName, dbName, e);
            return Map.of(KEY_SUCCESS, false, KEY_MESSAGE, e.getMessage());
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * Gets the primary key column for a table.
     *
     * @param dbName    the database name
     * @param tableName the table name
     * @return the primary key column name
     */
    private String getPrimaryKeyColumn(String dbName, String tableName) {
        try {
            // PostgreSQL query to get primary key column
            String sql = "select a.attname " + "from pg_index i " +
                    "join pg_attribute a on a.attrelid = i.indrelid and a.attnum = any(i.indkey) " +
                    "where i.indrelid = ?::regclass and i.indisprimary";

            return jdbcTemplate.queryForObject(sql, String.class, tableName);
        } catch (Exception e) {
            log.error("Error getting primary key for table: {} in database: {}", tableName, dbName, e);
            return null;
        }
    }

}
