package com.wshoto.jiali.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wshoto.jiali.domain.UsDepartmentUser;
import com.wshoto.jiali.mapper.UsDepartmentUserMapper;
import com.wshoto.jiali.service.UsDepartmentUserService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【us_department_user(部门成员关系数据)】的数据库操作Service实现
* @createDate 2024-02-05 15:26:25
*/
@DS("db_qa_wecom")
@Service
public class UsDepartmentUserServiceImpl extends ServiceImpl<UsDepartmentUserMapper, UsDepartmentUser>
        implements UsDepartmentUserService {

}




