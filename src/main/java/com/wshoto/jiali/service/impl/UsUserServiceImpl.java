package com.wshoto.jiali.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wshoto.jiali.domain.UsUser;
import com.wshoto.jiali.mapper.UsUserMapper;
import com.wshoto.jiali.service.UsUserService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/2/20 15:21
 */
@DS("db_qa_wecom")
@Service
public class UsUserServiceImpl extends ServiceImpl<UsUserMapper, UsUser> implements UsUserService {
}
