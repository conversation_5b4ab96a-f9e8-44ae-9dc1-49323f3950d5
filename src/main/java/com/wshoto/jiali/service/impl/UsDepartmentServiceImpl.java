package com.wshoto.jiali.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wshoto.jiali.domain.UsDepartment;
import com.wshoto.jiali.mapper.UsDepartmentMapper;
import com.wshoto.jiali.service.UsDepartmentService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【us_department(部门同步表)】的数据库操作Service实现
* @createDate 2024-02-05 15:25:37
*/
@DS("db_qa_wecom")
@Service
public class UsDepartmentServiceImpl extends ServiceImpl<UsDepartmentMapper, UsDepartment>
        implements UsDepartmentService {

}




