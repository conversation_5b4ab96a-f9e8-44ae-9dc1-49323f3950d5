package com.wshoto.jiali.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wshoto.jiali.config.JiaLiConfig;
import com.wshoto.jiali.domain.UsNotifyConfig;
import com.wshoto.jiali.enums.NotifyTypeEnum;
import com.wshoto.jiali.mapper.UsNotifyConfigMapper;
import com.wshoto.jiali.service.UsNotifyConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【us_notify_config(同步通知配置)】的数据库操作Service实现
* @createDate 2024-02-23 14:27:40
*/
@DS("db_qa_wecom")
@Service
public class UsNotifyConfigServiceImpl extends ServiceImpl<UsNotifyConfigMapper, UsNotifyConfig>
    implements UsNotifyConfigService{

    @Autowired
    private JiaLiConfig jiaLiConfig;

    /**
     * 邮件通知配置保存
     */
    @Override
    public Boolean saveUsNotifyConfig() {
        UsNotifyConfig usNotifyConfig= new UsNotifyConfig();
        usNotifyConfig.setTenantId(jiaLiConfig.getTenantId());
        usNotifyConfig.setNotifyType(NotifyTypeEnum.MAIL.getCode());
        usNotifyConfig.setIsOpen(1);
        usNotifyConfig.setMail(jiaLiConfig.getMail());
        usNotifyConfig.setUsername(jiaLiConfig.getUsername());
        usNotifyConfig.setPassword(jiaLiConfig.getPassword());
        usNotifyConfig.setHost(jiaLiConfig.getHost());
        usNotifyConfig.setPort(Long.valueOf(jiaLiConfig.getPort()));
        usNotifyConfig.setGmtCreate(DateUtil.date());
        usNotifyConfig.setGmtModified(DateUtil.date());
        usNotifyConfig.setSslEnable(Boolean.TRUE);
        usNotifyConfig.setNotifyType(2);
        usNotifyConfig.setTimeOut(30 * 1000);
        return this.save(usNotifyConfig);
    }

    @Override
    public int setNotifyEmails(String emails) {
        return this.getBaseMapper().setNotifyEmail(emails);
    }

    @Override
    public int deleteNotifyConfig() {
        List<UsNotifyConfig> allConfigs = this.lambdaQuery().list();
        if (CollectionUtils.isNotEmpty(allConfigs)) {
            for (UsNotifyConfig config : allConfigs) {
                this.getBaseMapper().deleteById(config.getId());
            }
        }
        return allConfigs.size();
    }

}




