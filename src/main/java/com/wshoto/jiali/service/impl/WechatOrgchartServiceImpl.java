package com.wshoto.jiali.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wshoto.jiali.config.JiaLiConfig;
import com.wshoto.jiali.domain.JialiUsSyncLog;
import com.wshoto.jiali.domain.UsDepartment;
import com.wshoto.jiali.domain.WechatOrgchart;
import com.wshoto.jiali.enums.DataStatusEnum;
import com.wshoto.jiali.enums.DeleteStatusEnum;
import com.wshoto.jiali.enums.JiaLiErrorSyncEnum;
import com.wshoto.jiali.mapper.UsDepartmentMapper;
import com.wshoto.jiali.mapper.WechatOrgchartMapper;
import com.wshoto.jiali.service.*;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpDepartmentService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpDepart;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【wechat_orgchart】的数据库操作Service实现
 * @createDate 2024-01-23 14:45:47
 */
@Slf4j
@DS("db_qa_wecom_datasync")
@Service
public class WechatOrgchartServiceImpl extends ServiceImpl<WechatOrgchartMapper, WechatOrgchart>
        implements WechatOrgchartService {


    @Autowired
    private WxCpService wxCpService;

    @Autowired
    private UsDepartmentService usDepartmentService;

    @Autowired
    private UsDepartmentMapper usDepartmentMapper;

    @Autowired
    private JialiUsSyncLogService jialiUsSyncLogService;

    @Autowired
    private JiaLiConfig jiaLiConfig;


    private final static  String INITTAGETDEPTID="1";
    private final static  String INITTAGETDEPTPARENTID="0";

    /**
     * 初始化-同步部门信息
     */
    @Deprecated
    @Override
    public void initOrgchart() {
        TimeInterval timer = DateUtil.timer();
        log.info("开始同步部门信息");
        //获取嘉里的部门信息
        List<WechatOrgchart> wechatOrgcharts = this.list();

        //本地-部门信息
        List<UsDepartment> usDepartments = usDepartmentService.list();

        //部门初始化
        if (CollectionUtils.isNotEmpty(usDepartments)) {
            log.info("部门信息已存在，同步任务取消");
            return;
        }
        initDept(wechatOrgcharts);
        log.info("初始化部门信息完成，耗时：{}", timer.interval());
    }

    /**
     * 增量-同步部门信息
     */
    @Override
    public void syncOrgchart() {
        TimeInterval timer = DateUtil.timer();
        log.info("增量-同步部门-->开始");

        //获取嘉里的部门信息
        List<WechatOrgchart> wechatOrgcharts = this.list();
        log.info("增量-同步部门-->嘉里部门数据大小:{}",wechatOrgcharts.size());
        //本地-部门信息
        List<UsDepartment> usDepartments = usDepartmentService.lambdaQuery().eq(UsDepartment::getDeleted, DeleteStatusEnum.NOT_DELETED.getCode()).list();
        log.info("增量-同步部门-->本地部门数据大小:{}",usDepartments.size());


        //部门增量更新
        addAndUpdateDept(wechatOrgcharts, usDepartments);

        log.info("增量-同步部门-->数据清洗完成-数据清洗时间:{}", timer.interval());
    }

    /**
     * 增量部门更新
     *
     * @param wechatOrgcharts
     * @param usDepartments
     */
    public void addAndUpdateDept(List<WechatOrgchart> wechatOrgcharts, List<UsDepartment> usDepartments) {
        List<UsDepartment> updateDeptList = new ArrayList<>();
        List<UsDepartment> addDeptList = new ArrayList<>();
        List<UsDepartment> delDeptList = new ArrayList<>();

        //获取需要删除的部门
        getDelDeptList(wechatOrgcharts, usDepartments, updateDeptList,delDeptList);
        //获取新增和更新的部门列表
        getAddAndUpdateDeptList(wechatOrgcharts,  usDepartments,updateDeptList, addDeptList);

        Set<UsDepartment> updateDeptListSet = new HashSet<>();
        Set<UsDepartment> addDeptListSet = new HashSet<>();
        updateDeptListSet.addAll(updateDeptList);
        addDeptListSet.addAll(addDeptList);

        List<UsDepartment> updateDeptListDis = new ArrayList<>();
        List<UsDepartment> addDeptListDis = new ArrayList<>();
        updateDeptListDis.addAll(updateDeptListSet);
        addDeptListDis.addAll(addDeptListSet);
        log.info("增量-同步部门-->新增部门数据:{},修改部门数据:{}", JSON.toJSONString(addDeptListDis),JSON.toJSONString(updateDeptListDis));
        log.info("增量-同步部门-->删除部门数据:{}",JSON.toJSONString(delDeptList));
        log.info("增量-同步部门-->新增部门数据大小:{},修改部门数据大小:{}", addDeptListDis.size(),updateDeptListDis.size());
        //批量新增和修改 部门信息
        addAndUpdateBatchDept(updateDeptListDis, addDeptListDis);
    }

    /**
     * 批量新增和修改 部门信息
     *
     * @param updateDeptList
     * @param addDeptList
     */
    public void addAndUpdateBatchDept(List<UsDepartment> updateDeptList, List<UsDepartment> addDeptList) {
        List<JialiUsSyncLog>  jialiUsSyncLogList = new ArrayList<>();
        for (UsDepartment usDepartment : addDeptList) {
            try {
                usDepartmentService.save(usDepartment);
            } catch (Exception e) {
                log.info("增量-同步部门-->部门新增--出现异常，异常信息：{},异常部门:{}",e.getMessage(), JSON.toJSONString(usDepartment));
                JialiUsSyncLog jialiUsSyncLog = new JialiUsSyncLog();
                jialiUsSyncLog.setErrorType(JiaLiErrorSyncEnum.DEPT_ADD.getCode());
                jialiUsSyncLog.setErrorDesc(e.getMessage());
                jialiUsSyncLog.setErrorJson(JSON.toJSONString(usDepartment));
                jialiUsSyncLog.setCreateTime(DateUtil.date());
                jialiUsSyncLog.setUpdateTime(DateUtil.date());
                jialiUsSyncLogList.add(jialiUsSyncLog);
            }
        }


        for (UsDepartment usDepartment : updateDeptList) {
            try {
                usDepartmentService.updateById(usDepartment);
            } catch (Exception e) {
                log.info("增量-同步部门-->部门修改--出现异常，异常信息：{},异常部门:{}",e.getMessage(), JSON.toJSONString(usDepartment));
                JialiUsSyncLog jialiUsSyncLog = new JialiUsSyncLog();
                jialiUsSyncLog.setErrorType(JiaLiErrorSyncEnum.DEPT_UPDATE.getCode());
                jialiUsSyncLog.setErrorDesc(e.getMessage());
                jialiUsSyncLog.setErrorJson(JSON.toJSONString(usDepartment));
                jialiUsSyncLog.setCreateTime(DateUtil.date());
                jialiUsSyncLog.setUpdateTime(DateUtil.date());
                jialiUsSyncLogList.add(jialiUsSyncLog);
            }
        }
        log.info("增量-同步部门-->异常的数据大小:{}",jialiUsSyncLogList.size());
        jialiUsSyncLogService.saveBatch(jialiUsSyncLogList);

    }

    private void getDelDeptList(List<WechatOrgchart> wechatOrgcharts, List<UsDepartment> usDepartments, List<UsDepartment> updateDeptList, List<UsDepartment> delDeptList) {
        //组装成树形结果和中间表比对
        List<UsDepartment> jiaLiDeptListTree = getUsDepartments(wechatOrgcharts);
        //获取当前嘉里树形部门ID
        List<String> jiaLiDeptIdList = jiaLiDeptListTree.stream().map(UsDepartment::getSourceDeptId).distinct().collect(Collectors.toList());
        //获取当前中间表树形部门ID
        List<String> wsDeptIdlist = usDepartments.stream().map(UsDepartment::getSourceDeptId).distinct().collect(Collectors.toList());
        //过滤出需要删除的部门ID
        List<String> differenceList = wsDeptIdlist.stream().filter(id -> !jiaLiDeptIdList.contains(id)).collect(Collectors.toList());
        //过滤出需要删除的部门信息
        delDeptList = usDepartments.stream().filter(usDepartment -> differenceList.contains(usDepartment.getSourceDeptId())).collect(Collectors.toList());
        log.info("增量-同步部门-->需要删除的部门大小:{},信息：{}", delDeptList.size(),JSON.toJSONString(delDeptList));

        //修改成删除状态
        for (UsDepartment usDepartment : delDeptList) {
            usDepartment.setDataStatus(DataStatusEnum.DELETE.getCode());
            usDepartment.setSyncStatus(DataStatusEnum.INIT.getCode());
            usDepartment.setGmtModified(DateUtil.date());
            updateDeptList.add(usDepartment);
        }
    }

    /**
     * 获取新增和更新的部门列表
     *
     * @param wechatOrgcharts
     * @param usDepartments
     * @param updateDeptList
     * @param addDeptList
     */
    private void getAddAndUpdateDeptList(List<WechatOrgchart> wechatOrgcharts,  List<UsDepartment> usDepartments, List<UsDepartment> updateDeptList, List<UsDepartment> addDeptList) {

        //获取每个部门节点的map
        Map<String, UsDepartment> usDepartmentsMap = usDepartments.stream().collect(Collectors.toMap(UsDepartment::getSourceDeptId, Function.identity(), (oldValue, newValue) -> newValue));

        for (WechatOrgchart wechatOrgchart : wechatOrgcharts) {

            log.info("增量-同步部门-->当前处理部门：{}", JSON.toJSONString(wechatOrgchart));
            String l1GroupCode = wechatOrgchart.getL1GroupCode();
            String l1GroupEn = wechatOrgchart.getL1GroupEn();
            String l1GroupCn = wechatOrgchart.getL1GroupCn();

            String l2DivisionCode = wechatOrgchart.getL2DivisionCode();
            String l2DivisionEn = wechatOrgchart.getL2DivisionEn();
            String l2DivisionCn = wechatOrgchart.getL2DivisionCn();
            updateDeptTree(usDepartmentsMap, l1GroupCode, l2DivisionCode, l2DivisionEn, l2DivisionCn, addDeptList, updateDeptList);


            String l4BusinessUnitCode = wechatOrgchart.getL4BusinessUnitCode();
            String l4BusinessUnitEn = wechatOrgchart.getL4BusinessUnitEn();
            String l4BusinessUnitCn = wechatOrgchart.getL4BusinessUnitCn();
            updateDeptTree(usDepartmentsMap, l2DivisionCode, l4BusinessUnitCode, l4BusinessUnitEn, l4BusinessUnitCn, addDeptList, updateDeptList);

            String l5ProjectCode = wechatOrgchart.getL5ProjectCode();
            String l5ProjectEn = wechatOrgchart.getL5ProjectEn();
            String l5ProjectCn = wechatOrgchart.getL5ProjectCn();
            updateDeptTree(usDepartmentsMap, l4BusinessUnitCode, l5ProjectCode, l5ProjectEn, l5ProjectCn, addDeptList, updateDeptList);


            String l7FunctionCode = wechatOrgchart.getL7FunctionCode();
            String l7FunctionEn = wechatOrgchart.getL7FunctionEn();
            String l7FunctionCn = wechatOrgchart.getL7FunctionCn();
            updateDeptTree(usDepartmentsMap, l5ProjectCode, l7FunctionCode, l7FunctionEn, l7FunctionCn, addDeptList, updateDeptList);

            String l8DepartmentCode = wechatOrgchart.getL8DepartmentCode();
            String l8DepartmentEn = wechatOrgchart.getL8DepartmentEn();
            String l8DepartmentCn = wechatOrgchart.getL8DepartmentCn();
            updateDeptTree(usDepartmentsMap, l7FunctionCode, l8DepartmentCode, l8DepartmentEn, l8DepartmentCn, addDeptList, updateDeptList);

        }
    }


    /**
     * 更新部门树
     *
     * @param usDepartmentsMap
     * @param parentCode
     * @param code
     * @param enName
     * @param cnName
     */
    private void updateDeptTree(Map<String, UsDepartment> usDepartmentsMap, String parentCode, String code, String enName, String cnName,
                                List<UsDepartment> addDeptList, List<UsDepartment> updateDeptList) {
        //获取当前部门节点信息
        UsDepartment usDepartment = usDepartmentsMap.get(code);

        log.info("数据库中当前部门的信息:{}",JSON.toJSONString(usDepartment));
        //新增部门
        if (Objects.isNull(usDepartment)) {
            UsDepartment addUsDepartment = getUsDepartment(parentCode, code, enName, cnName);
            addDeptList.add(addUsDepartment);
            return;
        }

        //更新部门
        String dbSourceDeptId = usDepartment.getSourceDeptId();
        String dbSourceDeptParentId = usDepartment.getSourceDeptParentId();
        String dbNameCn = usDepartment.getName();
        String dbNnameEn = usDepartment.getNameEn();


        if (!(Objects.equals(parentCode, dbSourceDeptParentId)
                && Objects.equals(cnName, dbNameCn)
                && Objects.equals(enName, dbNnameEn)
                && Objects.equals(code, dbSourceDeptId))) {

            usDepartment.setSourceDeptId(code);
            usDepartment.setSourceDeptParentId(parentCode);
            // en有值. cn没有值  拿en填到 企微的中文名,英文不填
            if(StringUtils.equals(enName,cnName)){
                String cnNames = limitAndValidateString(cnName);
                usDepartment.setName(cnNames);
                usDepartment.setNameEn(null);

            } else if(StringUtils.isNotEmpty(enName) && StringUtils.isEmpty(cnName)){
                String enNames = limitAndValidateString(enName);
                usDepartment.setName(enNames);
                usDepartment.setNameEn(null);
            } else if(StringUtils.isEmpty(enName) && StringUtils.isNotEmpty(cnName)){
                String cnNames = limitAndValidateString(cnName);
                usDepartment.setName(cnNames);
                usDepartment.setNameEn(enName);
            }else {
                String cnNames = limitAndValidateString(cnName);
                usDepartment.setName(cnNames);
                if (StringUtils.isNotEmpty(enName)) {
                    String enNames = limitAndValidateString(enName);
                    usDepartment.setNameEn(enNames);
                }
            }
            usDepartment.setDataStatus(DataStatusEnum.UPDATE.getCode());
            usDepartment.setSyncStatus(DataStatusEnum.INIT.getCode());
            usDepartment.setGmtModified(DateUtil.date());
            updateDeptList.add(usDepartment);
        }

    }


    /**
     * 初始化部门信息
     *
     * @param wechatOrgcharts
     */
    public void initDept(List<WechatOrgchart> wechatOrgcharts) {
        List<UsDepartment> deptList = getInitUsDepartments(wechatOrgcharts);
        //批量初始化保存
        usDepartmentService.saveBatch(deptList);

    }
    /**
     * 初始化组装树形结构
     *
     * @param wechatOrgcharts
     * @return
     */
    private List<UsDepartment> getInitUsDepartments(List<WechatOrgchart> wechatOrgcharts) {
        //初始化
        Set<UsDepartment> setl1 = new HashSet<>();
        Set<UsDepartment> setl2 = new HashSet<>();
        Set<UsDepartment> setl4 = new HashSet<>();
        Set<UsDepartment> setl5 = new HashSet<>();
        Set<UsDepartment> setl7 = new HashSet<>();
        Set<UsDepartment> setl8 = new HashSet<>();
        List<UsDepartment> deptList = new ArrayList<>();

        log.info("初始化-同步部门-->开始组装树形结构");
        //组装数据
        for (WechatOrgchart wechatOrgchart : wechatOrgcharts) {
            log.info("初始化-同步部门-->组装前的数据:{}",JSON.toJSONString(wechatOrgchart));
            //L1
            String l1GroupCode = wechatOrgchart.getL1GroupCode();
            String l1GroupEn = wechatOrgchart.getL1GroupEn();
            String l1GroupCn = wechatOrgchart.getL1GroupCn();
            //第一个节点 本地树形结构
            UsDepartment deptTree1 = new UsDepartment();
            deptTree1.setTenantId(jiaLiConfig.getTenantId());
            deptTree1.setSourceDeptId(l1GroupCode);
            deptTree1.setSourceDeptParentId(INITTAGETDEPTPARENTID);

            deptTree1.setTargetDeptId(INITTAGETDEPTID);
            deptTree1.setTargetDeptParentId(INITTAGETDEPTPARENTID);

            // en有值. cn没有值  拿en填到 企微的中文名,英文不填
            if(StringUtils.equals(l1GroupCn,l1GroupEn)){
                String cnNames = limitAndValidateString(l1GroupCn);
                deptTree1.setName(cnNames);
                deptTree1.setNameEn(null);

            } else if(StringUtils.isNotEmpty(l1GroupEn) && StringUtils.isEmpty(l1GroupCn)){
                String enNames = limitAndValidateString(l1GroupEn);
                deptTree1.setName(enNames);
                deptTree1.setNameEn(null);
            } else {
                String cnNames = limitAndValidateString(l1GroupCn);
                deptTree1.setName(cnNames);
                String enNames = limitAndValidateString(l1GroupCn);
                deptTree1.setNameEn(enNames);
            }
            deptTree1.setDataStatus(DataStatusEnum.INIT.getCode());
            deptTree1.setSyncStatus(DataStatusEnum.UPDATE.getCode());
            //第一个节点 本地树形结构
            //UsDepartment deptTreeL1 = getUsDepartment(null, l1GroupCode, l1GroupEn, l1GroupCn);


            //L2
            String l2DivisionCode = wechatOrgchart.getL2DivisionCode();
            String l2DivisionEn = wechatOrgchart.getL2DivisionEn();
            String l2DivisionCn = wechatOrgchart.getL2DivisionCn();
            //第2个节点 本地树形结构
            UsDepartment deptTreeL2 = getUsDepartment(l1GroupCode, l2DivisionCode, l2DivisionEn, l2DivisionCn);


            //L3
            String l3BusinessGroupCode = wechatOrgchart.getL3BusinessGroupCode();
            String l3BusinessGroupEn = wechatOrgchart.getL3BusinessGroupEn();
            String l3BusinessGroupCn = wechatOrgchart.getL3BusinessGroupCn();

            //L4
            String l4BusinessUnitCode = wechatOrgchart.getL4BusinessUnitCode();
            String l4BusinessUnitEn = wechatOrgchart.getL4BusinessUnitEn();
            String l4BusinessUnitCn = wechatOrgchart.getL4BusinessUnitCn();
            //第4个节点 本地树形结构
            UsDepartment deptTreeL4 = getUsDepartment(l2DivisionCode, l4BusinessUnitCode, l4BusinessUnitEn, l4BusinessUnitCn);


            //L5
            String l5ProjectCode = wechatOrgchart.getL5ProjectCode();
            String l5ProjectEn = wechatOrgchart.getL5ProjectEn();
            String l5ProjectCn = wechatOrgchart.getL5ProjectCn();
            //第5个节点 本地树形结构
            UsDepartment deptTreeL5 = getUsDepartment(l4BusinessUnitCode, l5ProjectCode, l5ProjectEn, l5ProjectCn);


            //L6
            String l6LegalEntityCode = wechatOrgchart.getL6LegalEntityCode();
            String l6LegalEntityEn = wechatOrgchart.getL6LegalEntityEn();
            String l6LegalEntityCn = wechatOrgchart.getL6LegalEntityCn();

            //L7
            String l7FunctionCode = wechatOrgchart.getL7FunctionCode();
            String l7FunctionEn = wechatOrgchart.getL7FunctionEn();
            String l7FunctionCn = wechatOrgchart.getL7FunctionCn();
            //第7个节点 本地树形结构
            UsDepartment deptTreeL7 = getUsDepartment(l5ProjectCode, l7FunctionCode, l7FunctionEn, l7FunctionCn);


            //L8
            String l8DepartmentCode = wechatOrgchart.getL8DepartmentCode();
            String l8DepartmentEn = wechatOrgchart.getL8DepartmentEn();
            String l8DepartmentCn = wechatOrgchart.getL8DepartmentCn();
            //第8个节点 本地树形结构
            UsDepartment deptTreeL8 = getUsDepartment(l7FunctionCode, l8DepartmentCode, l8DepartmentEn, l8DepartmentCn);
            setl8.add(deptTreeL8);
            setl7.add(deptTreeL7);
            setl5.add(deptTreeL5);
            setl4.add(deptTreeL4);
            setl2.add(deptTreeL2);
            setl1.add(deptTree1);
        }
        deptList.addAll(setl1);
        deptList.addAll(setl2);
        deptList.addAll(setl4);
        deptList.addAll(setl5);
        deptList.addAll(setl7);
        deptList.addAll(setl8);
        log.info("初始化-同步部门-->组装树形结构完成");
        return deptList;
    }
    /**
     * 组装树形结构
     *
     * @param wechatOrgcharts
     * @return
     */
    private List<UsDepartment> getUsDepartments(List<WechatOrgchart> wechatOrgcharts) {
        //初始化
        Set<UsDepartment> setl1 = new HashSet<>();
        Set<UsDepartment> setl2 = new HashSet<>();
        Set<UsDepartment> setl4 = new HashSet<>();
        Set<UsDepartment> setl5 = new HashSet<>();
        Set<UsDepartment> setl7 = new HashSet<>();
        Set<UsDepartment> setl8 = new HashSet<>();
        List<UsDepartment> deptList = new ArrayList<>();

        log.info("增量-同步部门-->开始组装树形结构");
        //组装数据
        for (WechatOrgchart wechatOrgchart : wechatOrgcharts) {
            log.info("增量-同步部门-->组装前的数据:{}",JSON.toJSONString(wechatOrgchart));

            //L1
            String l1GroupCode = wechatOrgchart.getL1GroupCode();
            String l1GroupEn = wechatOrgchart.getL1GroupEn();
            String l1GroupCn = wechatOrgchart.getL1GroupCn();
            //第一个节点 本地树形结构
            UsDepartment deptTreeL1 = getUsDepartment(null, l1GroupCode, l1GroupEn, l1GroupCn);


            //L2
            String l2DivisionCode = wechatOrgchart.getL2DivisionCode();
            String l2DivisionEn = wechatOrgchart.getL2DivisionEn();
            String l2DivisionCn = wechatOrgchart.getL2DivisionCn();
            //第2个节点 本地树形结构
            UsDepartment deptTreeL2 = getUsDepartment(l1GroupCode, l2DivisionCode, l2DivisionEn, l2DivisionCn);


            //L3
            String l3BusinessGroupCode = wechatOrgchart.getL3BusinessGroupCode();
            String l3BusinessGroupEn = wechatOrgchart.getL3BusinessGroupEn();
            String l3BusinessGroupCn = wechatOrgchart.getL3BusinessGroupCn();

            //L4
            String l4BusinessUnitCode = wechatOrgchart.getL4BusinessUnitCode();
            String l4BusinessUnitEn = wechatOrgchart.getL4BusinessUnitEn();
            String l4BusinessUnitCn = wechatOrgchart.getL4BusinessUnitCn();
            //第4个节点 本地树形结构
            UsDepartment deptTreeL4 = getUsDepartment(l2DivisionCode, l4BusinessUnitCode, l4BusinessUnitEn, l4BusinessUnitCn);


            //L5
            String l5ProjectCode = wechatOrgchart.getL5ProjectCode();
            String l5ProjectEn = wechatOrgchart.getL5ProjectEn();
            String l5ProjectCn = wechatOrgchart.getL5ProjectCn();
            //第5个节点 本地树形结构
            UsDepartment deptTreeL5 = getUsDepartment(l4BusinessUnitCode, l5ProjectCode, l5ProjectEn, l5ProjectCn);


            //L6
            String l6LegalEntityCode = wechatOrgchart.getL6LegalEntityCode();
            String l6LegalEntityEn = wechatOrgchart.getL6LegalEntityEn();
            String l6LegalEntityCn = wechatOrgchart.getL6LegalEntityCn();

            //L7
            String l7FunctionCode = wechatOrgchart.getL7FunctionCode();
            String l7FunctionEn = wechatOrgchart.getL7FunctionEn();
            String l7FunctionCn = wechatOrgchart.getL7FunctionCn();
            //第7个节点 本地树形结构
            UsDepartment deptTreeL7 = getUsDepartment(l5ProjectCode, l7FunctionCode, l7FunctionEn, l7FunctionCn);


            //L8
            String l8DepartmentCode = wechatOrgchart.getL8DepartmentCode();
            String l8DepartmentEn = wechatOrgchart.getL8DepartmentEn();
            String l8DepartmentCn = wechatOrgchart.getL8DepartmentCn();
            //第8个节点 本地树形结构
            UsDepartment deptTreeL8 = getUsDepartment(l7FunctionCode, l8DepartmentCode, l8DepartmentEn, l8DepartmentCn);
            setl8.add(deptTreeL8);
            setl7.add(deptTreeL7);
            setl5.add(deptTreeL5);
            setl4.add(deptTreeL4);
            setl2.add(deptTreeL2);
            setl1.add(deptTreeL1);
        }
        deptList.addAll(setl1);
        deptList.addAll(setl2);
        deptList.addAll(setl4);
        deptList.addAll(setl5);
        deptList.addAll(setl7);
        deptList.addAll(setl8);
        log.info("增量-同步部门-->组装树形结构完成");
        return deptList;
    }


    /**
     * 组装树形结构
     *
     * @param parentCode
     * @param code
     * @param enName
     * @param cnName
     * @return
     */
    private UsDepartment getUsDepartment(String parentCode, String code, String enName, String cnName) {
        UsDepartment deptTree = new UsDepartment();
        deptTree.setTenantId(jiaLiConfig.getTenantId());
        deptTree.setSourceDeptParentId(parentCode);
        if (StringUtils.isBlank(parentCode)) {
            deptTree.setTargetDeptId(INITTAGETDEPTID);
            deptTree.setTargetDeptParentId(INITTAGETDEPTPARENTID);
        }
        deptTree.setSourceDeptId(code);

        // en有值. cn没有值  拿en填到 企微的中文名,英文不填
        if(StringUtils.equals(enName,cnName)){
            String cnNames = limitAndValidateString(cnName);
            deptTree.setName(cnNames);
            deptTree.setNameEn(null);

        } else if(StringUtils.isNotEmpty(enName) && StringUtils.isEmpty(cnName)){
            String enNames = limitAndValidateString(enName);
            deptTree.setName(enNames);
            deptTree.setNameEn(null);
        } else {
            String cnNames = limitAndValidateString(cnName);
            deptTree.setName(cnNames);
            //英文名判空
            if (StringUtils.isNotEmpty(enName)) {
                String enNames = limitAndValidateString(enName);
                deptTree.setNameEn(enNames);
            }
        }


        deptTree.setDataStatus(DataStatusEnum.INSERT.getCode());
        deptTree.setSyncStatus(DataStatusEnum.INIT.getCode());
        return deptTree;
    }

    /**
     * 校验部门名称
     * @param input
     * @return
     */
    public String limitAndValidateString(String input) {
        // 长度限制为1到64个字符
        if (input.length() > 64) {
            // 如果字符串长度超过64，截取后64位
            input = input.substring(input.length() - 64);
            return input; // 或者抛出异常，视情况而定
        }
        //检查特殊字符
        if (input.contains("\\") || input.contains(":") || input.contains("*") ||
                input.contains("?") || input.contains("\"") || input.contains("<") ||
                input.contains(">") || input.contains("|")) {

            log.info("包含特殊字符:{}",input);
            return input;
        }

        return input;
    }

    /**
     * 批量初始化保存
     *
     * @param setl1
     * @param setl2
     * @param setl4
     * @param setl5
     * @param setl7
     * @param setl8
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(Set<UsDepartment> setl1, Set<UsDepartment> setl2, Set<UsDepartment> setl4, Set<UsDepartment> setl5, Set<UsDepartment> setl7, Set<UsDepartment> setl8) {
        usDepartmentService.saveBatch(setl1);
        usDepartmentService.saveBatch(setl2);
        usDepartmentService.saveBatch(setl4);
        usDepartmentService.saveBatch(setl5);
        usDepartmentService.saveBatch(setl7);
        usDepartmentService.saveBatch(setl8);
    }

    private static void updateDept(WxCpDepartmentService departmentService) throws WxErrorException {
        WxCpDepart wxCpDepart = new WxCpDepart();
        wxCpDepart.setId(1L);
        wxCpDepart.setName("嘉里建设");
        wxCpDepart.setEnName("Kerry Properties");
        departmentService.update(wxCpDepart);
    }
}




