package com.wshoto.jiali.service;

import java.util.List;
import java.util.Map;

/**
 * Database Manager Service.
 * Provides methods for database management operations.
 *
 * <AUTHOR>
 */
public interface DbManagerService {

    /**
     * Gets all available databases.
     *
     * @return list of database names
     */
    List<String> getDatabases();

    /**
     * Gets all tables for a specific database.
     *
     * @param dbName the database name
     * @return list of table names
     */
    List<String> getTables(String dbName);

    /**
     * Gets table structure for a specific table.
     *
     * @param dbName    the database name
     * @param tableName the table name
     * @return table structure information
     */
    List<Map<String, Object>> getTableStructure(String dbName, String tableName);

    /**
     * Gets table data with pagination.
     *
     * @param dbName    the database name
     * @param tableName the table name
     * @param page      the page number
     * @param size      the page size
     * @return paginated table data
     */
    Map<String, Object> getTableData(String dbName, String tableName, int page, int size);

    /**
     * Executes custom SQL query.
     *
     * @param dbName the database name
     * @param sql    the SQL query to execute
     * @return query results
     */
    Map<String, Object> executeSql(String dbName, String sql);

    /**
     * Updates table data.
     *
     * @param dbName    the database name
     * @param tableName the table name
     * @param id        the record id
     * @param data      the updated data
     * @return operation result
     */
    Map<String, Object> updateData(String dbName, String tableName, String id, Map<String, Object> data);

    /**
     * Deletes table data.
     *
     * @param dbName    the database name
     * @param tableName the table name
     * @param id        the record id
     * @return operation result
     */
    Map<String, Object> deleteData(String dbName, String tableName, String id);
}
