package com.wshoto.jiali.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wshoto.jiali.domain.UsUser;
import com.wshoto.jiali.domain.WechatActiveStaffinfo;

/**
* <AUTHOR>
* @description 针对表【wechat_active_staffinfo】的数据库操作Service
* @createDate 2024-01-29 17:06:01
*/
public interface WechatActiveStaffinfoService extends IService<WechatActiveStaffinfo> {


    /**
     * 同步员工信息
     */
    void syncStaffInfo();

    UsUser manuallyDisableStaff(String adUserName);

    UsUser manuallyExitedStaff(String adUserName);
}
