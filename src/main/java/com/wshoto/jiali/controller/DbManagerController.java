package com.wshoto.jiali.controller;

import com.wshoto.jiali.service.DbManagerService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * Database Manager Controller.
 * Provides endpoints for database management API.
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/db-manager/api")
@RequiredArgsConstructor
public class DbManagerController {

    private final DbManagerService dbManagerService;

    /**
     * Gets all available databases.
     *
     * @return list of database names
     */
    @GetMapping("/databases")
    public List<String> getDatabases() {
        return dbManagerService.getDatabases();
    }

    /**
     * Gets all tables for a specific database.
     *
     * @param dbName the database name
     * @return list of table names
     */
    @GetMapping("/tables")
    public List<String> getTables(@RequestParam String dbName) {
        return dbManagerService.getTables(dbName);
    }

    /**
     * Gets table structure for a specific table.
     *
     * @param dbName    the database name
     * @param tableName the table name
     * @return table structure information
     */
    @GetMapping("/table-structure")
    public List<Map<String, Object>> getTableStructure(@RequestParam String dbName, @RequestParam String tableName) {
        return dbManagerService.getTableStructure(dbName, tableName);
    }

    /**
     * Gets table data with pagination.
     *
     * @param dbName    the database name
     * @param tableName the table name
     * @param page      the page number
     * @param size      the page size
     * @return paginated table data
     */
    @GetMapping("/table-data")
    public Map<String, Object> getTableData(@RequestParam String dbName, @RequestParam String tableName,
                                            @RequestParam(defaultValue = "1") int page,
                                            @RequestParam(defaultValue = "20") int size) {
        return dbManagerService.getTableData(dbName, tableName, page, size);
    }

    /**
     * Executes custom SQL query.
     *
     * @param dbName the database name
     * @param sql    the SQL query to execute
     * @return query results
     */
    @PostMapping("/execute-sql")
    public Map<String, Object> executeSql(@RequestParam String dbName, @RequestParam String sql) {
        return dbManagerService.executeSql(dbName, sql);
    }

    /**
     * Updates table data.
     *
     * @param dbName    the database name
     * @param tableName the table name
     * @param id        the record id
     * @param data      the updated data
     * @return operation result
     */
    @PostMapping("/update-data")
    public Map<String, Object> updateData(@RequestBody UpdateData params) {
        return dbManagerService.updateData(params.dbName, params.tableName, params.id, params.data);
    }

    public record UpdateData(String dbName, String tableName, String id, Map<String, Object> data) {

    }

    /**
     * Deletes table data.
     *
     * @param dbName    the database name
     * @param tableName the table name
     * @param id        the record id
     * @return operation result
     */
    @DeleteMapping("/delete-data")
    public Map<String, Object> deleteData(@RequestParam String dbName, @RequestParam String tableName,
                                          @RequestParam String id) {
        return dbManagerService.deleteData(dbName, tableName, id);
    }

}
