package com.wshoto.jiali.controller;

import io.swagger.v3.oas.annotations.Hidden;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 嘉里k8s容器健康检查接口
 * */
@Slf4j
@Hidden
@RestController
public class HealthController {

    @GetMapping(value = "/health/shallow", produces = "application/json")
    public HealthResource healthProxy() {
        return HealthResource.SUCCESS_RESPONSE;
    }

    @Data
    public static class HealthResource {

        public static final int SUCCESS = 0;

        public static final HealthResource SUCCESS_RESPONSE = new HealthResource() {

            @Override
            public int getHealth() {
                return SUCCESS;
            }
        };

        public static final HealthResource FAIL_RESPONSE = new HealthResource();

        private int health = Integer.MIN_VALUE;

        public static boolean isSuccess(HealthResource healthResponse) {
            return Objects.nonNull(healthResponse) && SUCCESS == healthResponse.health;
        }

    }

}
