package com.wshoto.jiali.controller;

import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wshoto.jiali.config.WeComSyncConstant;
import com.wshoto.jiali.domain.UsDepartment;
import com.wshoto.jiali.domain.UsDepartmentUser;
import com.wshoto.jiali.domain.UsUser;
import com.wshoto.jiali.domain.WechatStuffDTO;
import com.wshoto.jiali.enums.DeleteStatusEnum;
import com.wshoto.jiali.mapper.UsDepartmentMapper;
import com.wshoto.jiali.service.DbManagerService;
import com.wshoto.jiali.service.UsDepartmentUserService;
import com.wshoto.jiali.service.UsNotifyConfigService;
import com.wshoto.jiali.service.UsUserService;
import com.wshoto.jiali.service.WechatActiveStaffinfoService;
import com.wshoto.jiali.service.WechatOrgchartService;
import com.wshoto.jiali.service.WechatStaffDTOService;
import io.micrometer.common.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/1/16 14:59
 */
@RestController
@Tag(name = "接口")
@Slf4j
public class WeComSyncController {

    @Autowired
    private DbManagerService dbManagerService;

    @Autowired
    private UsNotifyConfigService usNotifyConfigService;

    @Autowired
    private WechatActiveStaffinfoService wechatActiveStaffinfoService;

    @Autowired
    private WechatStaffDTOService wechatStaffDTOService;

    @Autowired
    private WechatOrgchartService wechatOrgchartService;

    @Autowired
    private UsDepartmentMapper usDepartmentMapper;

    @Autowired
    private UsUserService userService;

    @Autowired
    private UsDepartmentUserService usDepartmentUserService;

    /**
     * 邮件通知 配置初始化
     *
     * @return
     */
    @Deprecated
    @PostMapping("/initNotifyConfig")
    public Boolean saveNotifyConfig() {
        return usNotifyConfigService.saveUsNotifyConfig();
    }


    /**
     * 部门初始化
     */
    @Operation(hidden = true)
    @Deprecated
    @GetMapping("/initDept")
    public void initDept() {
        wechatOrgchartService.initOrgchart();
    }

    /**
     * 部门增量同步
     */
    @GetMapping("/syncDept")
    public void syncDept(){
        wechatOrgchartService.syncOrgchart();
    }

    /**
     * 员工同步
     */
    @Operation(hidden = true)
    @GetMapping("/initUser")
    public void initUser() {
        wechatActiveStaffinfoService.syncStaffInfo();
    }

    @PostMapping("/rePushHKDept/{targetDept}")
    public int rePushHKDept(@PathVariable("targetDept") String targetDept) {
        return usDepartmentMapper.rePushHKDepartment(targetDept);
    }

    @Operation(hidden = true)
    @PostMapping("/admin/notify/email/setting")
    public int setNotifyEmail(@RequestBody Map<String, String> map) {
        String emailAddress = map.get("emailAddress");
        if (StringUtils.isBlank(emailAddress)) {
            return 0;
        }
        return usNotifyConfigService.setNotifyEmails(emailAddress);
    }

    @Operation(hidden = true)
    @PostMapping("/admin/notify/config/delete")
    public int delNotifyConfig() {
        return usNotifyConfigService.deleteNotifyConfig();
    }

    @Operation(hidden = true)
    @PostMapping("/admin/manual/disable/{adUserName}")
    public UsUser manuallyDisableStaff(@PathVariable("adUserName") String adUserName) {
        return wechatActiveStaffinfoService.manuallyDisableStaff(adUserName);
    }

    @Operation(hidden = true)
    @PostMapping("/admin/manual/exit/{adUserName}")
    public UsUser manuallyExitedStaff(@PathVariable("adUserName") String adUserName) {
        return wechatActiveStaffinfoService.manuallyExitedStaff(adUserName);
    }

    @Operation(hidden = true)
    @PostMapping("/admin/query/dept/{deptId}")
    public Object queryDept(@PathVariable("deptId") String deptId) {
        if (NumberUtils.isDigits(deptId)) {
            return usDepartmentMapper.queryByTargetDeptId(deptId);
        } else {
            return usDepartmentMapper.queryBySourceDeptId(deptId);
        }
    }

    @Operation(hidden = true)
    @PutMapping("/admin/update/dept")
    public Object updateDept(@RequestBody UsDepartment department) {
        usDepartmentMapper.updateById(department);
        return queryDept(department.getTargetDeptId());
    }

    @Operation(hidden = true)
    @GetMapping("admin/ususerinfo/data_status/{dataStatus}/sync_status/{syncStatus}")
    public Object queryUserByStatus(@PathVariable("dataStatus") Integer dataStatus
            , @PathVariable("syncStatus") Integer syncStatus) {
        return userService.lambdaQuery()
                .eq(UsUser::getDataStatus, dataStatus)
                .eq(UsUser::getSyncStatus, syncStatus).list();
    }

    @Operation(hidden = true)
    @PutMapping("admin/ususerinfo/status")
    public Object updateUsUserStatus(@RequestBody Map<String, Object> request) {
        String adUserName = String.valueOf(request.get("ad_username"));
        List<UsUser> dbUserList = userService.lambdaQuery()
                .in(UsUser::getSourceUserid, adUserName)
                .eq(UsUser::getDeleted, DeleteStatusEnum.NOT_DELETED.getCode()).list();
        if (CollectionUtils.isEmpty(dbUserList)) {
            return "查寻usUser为空";
        }
        if (request.keySet().size() > 1) {
            int all = Objects.nonNull(request.get("all")) ? Integer.parseInt(String.valueOf(request.get("all"))) : 0;
            Stream<UsUser> stream;
            if (0 == all) {
                stream = Stream.of(dbUserList.get(0));
            } else {
                stream = dbUserList.stream();
            }
            stream.forEach(usUser -> {
                if (Objects.nonNull(request.get("dataStatus"))) {
                    usUser.setDataStatus(Integer.valueOf(String.valueOf(request.get("dataStatus"))));
                }
                if (Objects.nonNull(request.get("syncStatus"))) {
                    usUser.setSyncStatus(Integer.valueOf(String.valueOf(request.get("syncStatus"))));
                }
                if (Objects.nonNull(request.get("directLeader"))) {
                    usUser.setDirectLeader(String.valueOf(request.get("directLeader")));
                }
                if (Objects.nonNull(request.get("deleted"))) {
                    usUser.setDeleted(Integer.valueOf(String.valueOf(request.get("deleted"))));
                }
                if (Objects.nonNull(request.get("enable"))) {
                    usUser.setEnable(Integer.valueOf(String.valueOf(request.get("enable"))));
                }
                if (Objects.nonNull(request.get("mainDepartment"))) {
                    usUser.setMainDepartment(Integer.valueOf(String.valueOf(request.get("mainDepartment"))));
                }
                userService.updateById(usUser);
            });
        }
        return userService.lambdaQuery()
                .in(UsUser::getSourceUserid, adUserName)
                .eq(UsUser::getDeleted, DeleteStatusEnum.NOT_DELETED.getCode()).list();
    }

    /* 通过deptId获取部门的员工*/
    @Operation(hidden = true)
    @PostMapping("admin/department_user/list")
    public Object updateUsDepartUser(@RequestBody Map<String, Object> request) {
        String targetDeptId = Optional.ofNullable(request.get("deptId")).map(String::valueOf).orElse(null);
        String adUserName = Optional.ofNullable(request.get("ad_username")).map(String::valueOf).orElse(null);
        Integer delFlag = Optional.ofNullable(request.get("del")).map(String::valueOf)
                .map(Integer::valueOf).orElse(null);
        LambdaQueryWrapper<UsDepartmentUser> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(delFlag)) {
            queryWrapper.eq(UsDepartmentUser::getDeleted, delFlag);
        }
        if (StringUtils.isNotBlank(adUserName)) {
            queryWrapper.eq(UsDepartmentUser::getTartgetUserid, adUserName);
        }
        if (StringUtils.isNotBlank(targetDeptId)) {
            queryWrapper.eq(UsDepartmentUser::getTargetDeptId, targetDeptId);
        }
        return usDepartmentUserService.list(queryWrapper);
    }

    /* 通过deptId获取部门的员工*/
    @Operation(hidden = true)
    @PutMapping("admin/department_user")
    public Object updateUsDepartUser(@RequestBody UsDepartmentUser departmentUser) {
        log.info("update_depart_user: {}", departmentUser);
        return usDepartmentUserService.updateById(departmentUser);
    }

    @Operation(hidden = true)
    @PostMapping("admin/update_external_sync/{val}")
    public Object updateExternalSync(@PathVariable("val") String val) {
        WeComSyncConstant.setSyncExternalCorpName(val);
        return WeComSyncConstant.SYNC_EXTERNAL_CORP_NAME;
    }

    @DS("db_qa_wecom")
    @Operation(hidden = true)
    @PostMapping("admin/us/execute_sql")
    public Object adminExecuteSql(@RequestBody String sql) {
        if (StringUtils.isBlank(sql)) {
            return "sql不能为空";
        }

        log.info("Executing SQL: {}", sql);
        String trimmedSql = sql.trim()
                               .toLowerCase();
        boolean isSelect = trimmedSql.startsWith("select");
        Map<String, Object> result = dbManagerService.executeSql("db_qa_wecom", sql);

        if (result == null) {
            return "sql执行失败，结果为空";
        }

        if (isSelect) {
            if (result.get("success") != null && Boolean.TRUE.equals(result.get("success"))) {
                return result.get("data");
            }
        }
        return result;
    }

    /**
     * tick4.5发版时使用，给已有1200+已登录员工在us_user表中填充aad_id，以供后续删除时比对
     */
    @Operation(hidden = true)
    @DS("db_qa_wecom_datasync")
    @PutMapping("admin/us_users/sync_aad_id")
    public List<UsUser> syncUsUsersAadId() {
        List<WechatStuffDTO> wechatStuffDTOList =  wechatStaffDTOService.list();
        Map<String, WechatStuffDTO> wechatStuffDTOMap = wechatStuffDTOList.stream()
                .collect(Collectors.toMap(WechatStuffDTO::getAdUsername, Function.identity(), (o, n) -> o));

        log.info("sync aad id user, sync user");
        List<UsUser> usUsersAadIdEmpty = userService.list().stream()
                .filter(user -> DeleteStatusEnum.NOT_DELETED.getCode().equals(user.getDeleted()))
                .filter(user -> StringUtils.isEmpty(user.getAadId())).toList();
        usUsersAadIdEmpty.forEach(usUser -> {
            WechatStuffDTO wechatStuffDTOTemp = wechatStuffDTOMap.get(usUser.getSourceUserid());
            if (Objects.isNull(wechatStuffDTOTemp)) {
                log.error("sync aad id user, wechatStuffDTOTemp not found, {}", JSONUtil.toJsonStr(usUser));
                return;
            }

            usUser.setAadId(wechatStuffDTOTemp.getAadId());
            userService.saveOrUpdate(usUser);
        });

        return userService.list().stream()
                .filter(user -> DeleteStatusEnum.NOT_DELETED.getCode().equals(user.getDeleted()))
                .filter(user -> StringUtils.isEmpty(user.getAadId())).toList();
    }

    /**
     * tick4.5发版时使用，给已有1200+已登录员工在us_department_user表中填充aad_id，以供后续删除时比对
     */
    @Operation(hidden = true)
    @DS("db_qa_wecom_datasync")
    @PutMapping("admin/us_department_user/sync_aad_id")
    public List<UsDepartmentUser> syncUsDepartmentUserAadId() {
        List<WechatStuffDTO> wechatStuffDTOList =  wechatStaffDTOService.list();
        Map<String, WechatStuffDTO> wechatStuffDTOMap = wechatStuffDTOList.stream()
                .collect(Collectors.toMap(WechatStuffDTO::getAdUsername, Function.identity(), (o, n) -> o));

        log.info("sync aad id user, sync depart user");
        List<UsDepartmentUser> usDepartmentUserAadIdEmpty = usDepartmentUserService.list().stream()
                .filter(usDepartmentUser -> DeleteStatusEnum.NOT_DELETED.getCode().equals(usDepartmentUser.getDeleted()))
                .filter(usDepartmentUser -> StringUtils.isEmpty(usDepartmentUser.getAadId())).toList();
        usDepartmentUserAadIdEmpty.forEach(usDepartmentUser -> {
            WechatStuffDTO wechatStuffDTOTemp = wechatStuffDTOMap.get(usDepartmentUser.getTartgetUserid());
            if (Objects.isNull(wechatStuffDTOTemp)) {
                log.error("sync aad id us department user, wechatStuffDTOTemp not found, {}", JSONUtil.toJsonStr(usDepartmentUser));
                return;
            }

            usDepartmentUser.setAadId(wechatStuffDTOTemp.getAadId());
            usDepartmentUserService.saveOrUpdate(usDepartmentUser);
        });

        return usDepartmentUserService.list().stream()
                .filter(usDepartmentUser -> DeleteStatusEnum.NOT_DELETED.getCode().equals(usDepartmentUser.getDeleted()))
                .filter(usDepartmentUser -> StringUtils.isEmpty(usDepartmentUser.getAadId())).toList();
    }


}
