package com.wshoto.jiali.config;

import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @date 2024/1/14 15:52
 */

@Configuration
@EnableConfigurationProperties(WxCpProperties.class)
public class WxCpConfig {

    @Autowired
    public void WxCpConfiguration(WxCpProperties properties) {
        this.properties = properties;
    }

    private WxCpProperties properties;


    @Bean
    public WxCpService wxCpConfigMap() {

        WxCpProperties appConfig = this.properties;
        WxCpService service = new WxCpServiceImpl();
        WxCpDefaultConfigImpl configStorage = new WxCpDefaultConfigImpl();
        configStorage.setCorpId(appConfig.getCorpId());
        configStorage.setAgentId(appConfig.getAgentId());
        configStorage.setCorpSecret(appConfig.getSecret());
        configStorage.setToken(appConfig.getToken());
        configStorage.setAesKey(appConfig.getAesKey());
        service.setWxCpConfigStorage(configStorage);

        return service;
    }
}
