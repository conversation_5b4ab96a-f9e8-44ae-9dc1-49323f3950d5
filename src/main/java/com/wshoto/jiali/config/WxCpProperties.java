package com.wshoto.jiali.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */

@Data
@ConfigurationProperties(prefix = "wechat.cp")
public class  WxCpProperties {

    /**
     * 设置企业微信的corpId
     */

    private String corpId;
    /**
     * 设置企业微信应用的AgentId
     */
    private Integer agentId;

    /**
     * 设置企业微信应用的Secret
     */
    private String secret;

    /**
     * 设置企业微信应用的token
     */
    private String token;

    /**
     * 设置企业微信应用的EncodingAESKey
     */
    private String aesKey;
    /**
     * 企业名称
     * 自定义字段 方便记录日志
     */
    private String corpName;

}
