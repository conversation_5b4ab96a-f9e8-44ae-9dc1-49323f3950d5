package com.wshoto.jiali.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;
@Data
@Component
@Configuration
public class JiaLiConfig {

    @Value("${jiali.whiteDept}")
    private List<String> whiteDept;
    @Value("${jiali.whiteDeptL2}")
    private List<String> whiteDeptL2;
    @Value("${jiali.tenantid}")
    private String tenantId;
    @Value("${jiali.mail}")
    private String mail;
    @Value("${jiali.username}")
    private String username;
    @Value("${jiali.password}")
    private String password;
    @Value("${jiali.host}")
    private String host;
    @Value("${jiali.port}")
    private String port;



    /**
     * 对外职务
     */
    @Value("${jiali.externalPosition}")
    private String externalPosition;


    /**
     * 对外职务
     */
    @Value("${jiali.extattrName}")
    private String extattrName;


    /**
     * 企业简称
     */
    @Value("${jiali.externalCorpName}")
    private String externalCorpName;




}
