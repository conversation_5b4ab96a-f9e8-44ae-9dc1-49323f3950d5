package com.wshoto.jiali.job;

import com.wshoto.jiali.filter.WeComConversationFilter;
import com.wshoto.jiali.service.WechatActiveStaffinfoService;
import com.wshoto.jiali.service.WechatOrgchartService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/3/15 10:29
 */
@Slf4j
@Component
@ConditionalOnProperty(value = "distributed.jobs.enabled",
                       havingValue = "true")
@RequiredArgsConstructor
public class ContactsJob {

    private final WechatOrgchartService wechatOrgchartService;

    private final WechatActiveStaffinfoService wechatActiveStaffinfoService;


    @Scheduled(cron = "0 20 2 * * ?")
    public void syncOrgchart() {
        WeComConversationFilter.setLoggingContext();
        log.info("同步部门开始");
        wechatOrgchartService.syncOrgchart();
        log.info("同步部门结束");
    }

    @Scheduled(cron = "0 45 3 * * ?")
    public void syncStaffInfo() {
        WeComConversationFilter.setLoggingContext();
        log.info("同步员工开始");
        wechatActiveStaffinfoService.syncStaffInfo();
        log.info("同步员工结束");
    }

}
