package com.wshoto.jiali.filter;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.UUID;

@Component
public class WeComConversationFilter implements Filter {

    public static final String MDC_APPLICATION_ID_KEY = "ApplicationId";

    public static final String MDC_CORRELATION_ID_KEY = "CorrelationId";

    public static final String MDC_CONVERSATION_ID_KEY = "ConversationId";

    public static final String MDC_PAGE_ID_KEY = "PageId";

    public static final String CONVERSATION_ID_HEADER = "X-Conversation-Id";

    public static final String APP_ID_HEADER = "X-App-Id";

    public static final String CORRELATION_ID_HEADER = "X-Correlation-ID";

    public static final String PAGE_ID_HEADER = "X-Page-ID";

    private static final String GENERATED_UNIQUE_ID_PREFIX = "BS-";

    private static final int GENERATED_UNIQUE_ID_SIZE_LIMIT = 20;

    private static final String MISSING_APP_ID_SUBSTITUTE = "NONE";

    public static void setLoggingContext() {
        String conversationId = generateUniqueId();
        ThreadContext.put(MDC_CONVERSATION_ID_KEY, conversationId);
        String correlationId = generateUniqueId();
        ThreadContext.put(MDC_CORRELATION_ID_KEY, correlationId);
    }

    public static void setLoggingContext(String conversationId, String correlationId) {
        if (StringUtils.isEmpty(conversationId)) {
            conversationId = generateUniqueId();
        }
        ThreadContext.put(MDC_CONVERSATION_ID_KEY, conversationId);
        if (StringUtils.isEmpty(correlationId)) {
            correlationId = generateUniqueId();
        }
        ThreadContext.put(MDC_CORRELATION_ID_KEY, correlationId);
    }

    public static void refreshCorrelationId() {
        ThreadContext.put(MDC_CORRELATION_ID_KEY, generateUniqueId());
    }

    public static String getCorrelationId() {
        return ThreadContext.get(MDC_CORRELATION_ID_KEY);
    }

    public static String getConversationId() {
        return ThreadContext.get(MDC_CONVERSATION_ID_KEY);
    }

    public static void setLoggingContext(String conversationId, String correlationId, String applicationId, String pageId) {
        setLoggingContext(conversationId, correlationId);
        if (StringUtils.isNotEmpty(applicationId)) {
            ThreadContext.put(MDC_APPLICATION_ID_KEY, applicationId);
        }
        if (StringUtils.isNotEmpty(pageId)) {
            ThreadContext.put(MDC_PAGE_ID_KEY, pageId);
        }
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        String conversationId = request.getHeader(CONVERSATION_ID_HEADER);
        String applicationId = request.getHeader(APP_ID_HEADER);
        String correlationId = request.getHeader(CORRELATION_ID_HEADER);
        String pageId = request.getHeader(PAGE_ID_HEADER);
        if (StringUtils.isEmpty(conversationId)) {
            conversationId = generateUniqueId();
        }
        conversationId = URLEncoder.encode(conversationId, response.getCharacterEncoding());
        response.setHeader(CONVERSATION_ID_HEADER, conversationId);
        if (applicationId == null) {
            applicationId = MISSING_APP_ID_SUBSTITUTE;
        } else {
            applicationId = URLEncoder.encode(applicationId, response.getCharacterEncoding());
            response.setHeader(APP_ID_HEADER, applicationId);
        }
        if (StringUtils.isEmpty(correlationId)) {
            correlationId = generateUniqueId();
        }
        if (pageId == null) {
            pageId = "";
        }
        response.setHeader(PAGE_ID_HEADER, URLEncoder.encode(pageId, response.getCharacterEncoding()));
        correlationId = URLEncoder.encode(correlationId, response.getCharacterEncoding());
        response.setHeader(CORRELATION_ID_HEADER, correlationId);
        ThreadContext.put(MDC_CONVERSATION_ID_KEY, conversationId);
        ThreadContext.put(MDC_APPLICATION_ID_KEY, applicationId);
        ThreadContext.put(MDC_CORRELATION_ID_KEY, correlationId);
        ThreadContext.put(MDC_PAGE_ID_KEY, pageId);
        chain.doFilter(request, response);
    }

    private static String generateUniqueId() {
        String s = UUID.randomUUID().toString();
        int currentLength = s.length() + GENERATED_UNIQUE_ID_PREFIX.length();
        if (GENERATED_UNIQUE_ID_SIZE_LIMIT > 0 && currentLength > GENERATED_UNIQUE_ID_SIZE_LIMIT) {
            int charsToRemove = GENERATED_UNIQUE_ID_SIZE_LIMIT - GENERATED_UNIQUE_ID_PREFIX.length();
            s = StringUtils.right(s, charsToRemove);
        }
        return GENERATED_UNIQUE_ID_PREFIX + s;
    }

}
