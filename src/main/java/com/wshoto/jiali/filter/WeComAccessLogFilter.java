package com.wshoto.jiali.filter;


import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Enumeration;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class WeComAccessLogFilter implements Filter {

    private static final List<String> LOG_REQ_HEADERS = List.of("x-request-id", "referer", "accept", "accept-language"
            , "accept-encoding", "user-agent", "content-length", "brandid", "lbsid", "appid");

    private static final List<String> SKIP_APIS = List.of("/health/shallow");

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse
            , FilterChain chain) throws ServletException, IOException {

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        AccessLogResponseWrapper responseWrapper = new AccessLogResponseWrapper(response);
        long start = System.currentTimeMillis();
//        try {
        chain.doFilter(servletRequest, responseWrapper);
//        } catch (Exception e) {
//            log.error("AccessLogFilter: Exception_occurred_during chain.doFilter for URI: {}"
//                    , getFirstLineOfRequest(request));
//            log.error("error: ", e);
//        }
        long end = System.currentTimeMillis();
        String message = formatLog(request, responseWrapper, start, end);
        if (StringUtils.isNotBlank(message)) {
            log.info(message);
        }
    }

    protected String formatLog(HttpServletRequest request, AccessLogResponseWrapper responseWrapper, long start, long end) {
        String firstLineOfReq = getFirstLineOfRequest(request);
        if (StringUtils.isEmpty(firstLineOfReq) || isSkipReq(firstLineOfReq)) {
            return null;
        }
        StringBuilder buf = new StringBuilder(512);
        buf.append("request-method=").append(request.getMethod()).append(" status-code=")
                .append(responseWrapper.getStatus()).append(" request=\"")
                .append(firstLineOfReq)
                .append('"')
                .append(" response-millis=")
                .append(end - start);
        Enumeration<String> headerNames = request.getHeaderNames();
        if (headerNames != null) {
            buf.append(" headers=[");
            String headerName = null;
            while (headerNames.hasMoreElements()) {
                if (Objects.nonNull(headerName)) {
                    headerName = Optional.ofNullable(headerNames.nextElement()).map(String::toLowerCase).orElse("");
                    if (LOG_REQ_HEADERS.contains(headerName)) {
                        buf.append(", ").append(headerName).append("=").append(request.getHeader(headerName));
                    }
                } else {
                    headerName = Optional.ofNullable(headerNames.nextElement()).map(String::toLowerCase).orElse("");
                    if (LOG_REQ_HEADERS.contains(headerName)) {
                        buf.append(" ").append(headerName).append("=").append(request.getHeader(headerName));
                    }
                }
            }
            buf.append(" ]");
        }
        return buf.toString();
    }

    private String getFirstLineOfRequest(HttpServletRequest request) {
        StringBuilder buf = new StringBuilder();
        if (request != null) {
            buf.append(request.getMethod());
            buf.append(' ');
            buf.append(request.getRequestURI());
            if (request.getQueryString() != null) {
                buf.append('?');
                buf.append(request.getQueryString());
            }
            buf.append(' ');
            buf.append(request.getProtocol());
        } else {
            buf.append("- - ");
        }
        return buf.toString();
    }

    private boolean isSkipReq(String firstLineOfReq) {
        for (String skipAPI : SKIP_APIS) {
            if (firstLineOfReq.contains(skipAPI)) {
                return true;
            }
        }
        return false;
    }

    public static class AccessLogResponseWrapper extends HttpServletResponseWrapper {

        private int status;

        public AccessLogResponseWrapper(HttpServletResponse response) {
            super(response);
        }

        @Override
        public int getStatus() {
            return this.status;
        }

        @Override
        public void setStatus(int status) {
            this.status = status;
            super.setStatus(status);
        }

    }

}
