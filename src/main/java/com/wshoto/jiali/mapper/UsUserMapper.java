package com.wshoto.jiali.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wshoto.jiali.domain.UsUser;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【us_user(成员同步表)】的数据库操作Mapper
* @createDate 2024-02-05 15:27:13
* @Entity com.wshoto.jiali.domain.UsUser
*/
@DS("db_qa_wecom")
@Mapper
public interface UsUserMapper extends BaseMapper<UsUser> {
    void  updateUsUser(UsUser usUser);
}




