package com.wshoto.jiali.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wshoto.jiali.domain.UsNotifyConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
* <AUTHOR>
* @description 针对表【us_notify_config(同步通知配置)】的数据库操作Mapper
* @createDate 2024-02-23 14:27:40
* @Entity generator.domain.UsNotifyConfig
*/
@Mapper
public interface UsNotifyConfigMapper extends BaseMapper<UsNotifyConfig> {

    @Update("update us_notify_config set to_user = '${email}' where 1=1")
    int setNotifyEmail(@Param("email") String email);

}




