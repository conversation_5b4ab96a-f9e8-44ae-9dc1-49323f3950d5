package com.wshoto.jiali.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wshoto.jiali.domain.UsDepartment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【us_department(部门同步表)】的数据库操作Mapper
* @createDate 2024-02-05 15:25:37
* @Entity com.wshoto.jiali.domain.UsDepartment
*/
@DS("db_qa_wecom")
@Mapper
public interface UsDepartmentMapper extends BaseMapper<UsDepartment> {

    /**
     * 批量修改部门数据
     * @param departments
     */
    void batchUpdateDepartment(@Param("list") List<UsDepartment> departments);

    @Update("update us_user\n" +
            "set data_status  = 1,\n" +
            "    sync_status=0,\n" +
            "    direct_leader= null\n" +
            "where main_department in (select CAST(target_dept_id AS INTEGER)\n" +
            "                          from us_department_user\n" +
            "                          where source_dept_id in ('${targetDept}'))\n" +
            "  and data_status != 3\n" +
            "  and deleted = 0;\n")
    int rePushHKDepartment(@Param("targetDept") String targetDept);

    List<UsDepartment> queryBySourceDeptId(@Param("deptId") String deptId);

    List<UsDepartment> queryByTargetDeptId(@Param("deptId") String deptId);

}




