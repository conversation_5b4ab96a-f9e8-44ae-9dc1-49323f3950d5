package com.wshoto.jiali.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName wechat_inactive_staffinfo
 */
@TableName(value ="wechat_inactive_staffinfo")
@Data
public class WechatInactiveStaffinfo implements Serializable {
    /**
     * 
     */
    @TableField(value = "staff_no")
    private String staffNo;

    /**
     * 
     */
    @TableField(value = "system_sid")
    private String systemSid;

    /**
     * 
     */
    @TableField(value = "ad_username")
    private String adUsername;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}