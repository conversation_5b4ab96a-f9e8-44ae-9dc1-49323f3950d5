package com.wshoto.jiali.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 数据清洗入库异常日志
 * @TableName jiali_us_sync_log
 */
@TableName(value ="jiali_us_sync_log")
@Data
public class JialiUsSyncLog implements Serializable {
    /**
     * 
     */
    @TableId(value = "id" , type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 
     */
    @TableField(value = "parent_dept_id")
    private String parentDeptId;

    /**
     * 
     */
    @TableField(value = "dept_id")
    private String deptId;

    /**
     * 
     */
    @TableField(value = "error_type")
    private Integer errorType;

    /**
     * 错误信息
     */
    @TableField(value = "error_desc")
    private String errorDesc;

    /**
     * 错误的json信息
     */
    @TableField(value = "error_json")
    private String errorJson;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}