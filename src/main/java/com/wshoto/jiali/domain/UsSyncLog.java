package com.wshoto.jiali.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 同步日志
 * @TableName us_sync_log
 */
@TableName(value ="us_sync_log")
@Data
public class UsSyncLog implements Serializable {
    /**
     * 主键
     */
    @TableField(value = "id")
    private Long id;

    /**
     * 
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 接口地址
     */
    @TableField(value = "interface_url")
    private String interfaceUrl;

    /**
     * 参数
     */
    @TableField(value = "interface_param")
    private String interfaceParam;

    /**
     * 返回结果
     */
    @TableField(value = "interface_result")
    private String interfaceResult;

    /**
     * 接口状态
     */
    @TableField(value = "status")
    private String status;

    /**
     * 当前版本
     */
    @TableField(value = "version")
    private Integer version;

    /**
     * 删除标记  0未删除 1已删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private Date gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}