package com.wshoto.jiali.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 企微员工-禁用/删除赦免名单表
 *
 * <AUTHOR>
 * @date 2025-1-23
 */
@Data
@TableName(value = "wechat_inactive_exclude_staffinfo")
public class WechatInactiveExcludeStaffinfo implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(value = "ad_username")
    private String adUsername;

}