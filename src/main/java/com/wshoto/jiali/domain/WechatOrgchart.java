package com.wshoto.jiali.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName wechat_orgchart
 */
@TableName(value ="wechat_orgchart")
@Data
public class WechatOrgchart implements Serializable {
    /**
     * 
     */
    @TableField(value = "l1_group_code")
    private String l1GroupCode;

    /**
     * 
     */
    @TableField(value = "l1_group_en")
    private String l1GroupEn;

    /**
     * 
     */
    @TableField(value = "l1_group_cn")
    private String l1GroupCn;

    /**
     * 
     */
    @TableField(value = "l2_division_code")
    private String l2DivisionCode;

    /**
     * 
     */
    @TableField(value = "l2_division_en")
    private String l2DivisionEn;

    /**
     * 
     */
    @TableField(value = "l2_division_cn")
    private String l2DivisionCn;

    /**
     * 
     */
    @TableField(value = "l3_business_group_code")
    private String l3BusinessGroupCode;

    /**
     * 
     */
    @TableField(value = "l3_business_group_en")
    private String l3BusinessGroupEn;

    /**
     * 
     */
    @TableField(value = "l3_business_group_cn")
    private String l3BusinessGroupCn;

    /**
     * 
     */
    @TableField(value = "l4_business_unit_code")
    private String l4BusinessUnitCode;

    /**
     * 
     */
    @TableField(value = "l4_business_unit_en")
    private String l4BusinessUnitEn;

    /**
     * 
     */
    @TableField(value = "l4_business_unit_cn")
    private String l4BusinessUnitCn;

    /**
     * 
     */
    @TableField(value = "l5_project_code")
    private String l5ProjectCode;

    /**
     * 
     */
    @TableField(value = "l5_project_en")
    private String l5ProjectEn;

    /**
     * 
     */
    @TableField(value = "l5_project_cn")
    private String l5ProjectCn;

    /**
     * 
     */
    @TableField(value = "l6_legal_entity_code")
    private String l6LegalEntityCode;

    /**
     * 
     */
    @TableField(value = "l6_legal_entity_en")
    private String l6LegalEntityEn;

    /**
     * 
     */
    @TableField(value = "l6_legal_entity_cn")
    private String l6LegalEntityCn;

    /**
     * 
     */
    @TableField(value = "l7_function_code")
    private String l7FunctionCode;

    /**
     * 
     */
    @TableField(value = "l7_function_en")
    private String l7FunctionEn;

    /**
     * 
     */
    @TableField(value = "l7_function_cn")
    private String l7FunctionCn;

    /**
     * 
     */
    @TableField(value = "l8_department_code")
    private String l8DepartmentCode;

    /**
     * 
     */
    @TableField(value = "l8_department_en")
    private String l8DepartmentEn;

    /**
     * 
     */
    @TableField(value = "l8_department_cn")
    private String l8DepartmentCn;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}