package com.wshoto.jiali.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 成员同步表
 * @TableName us_user
 */
@TableName(value ="us_user")
@Data
public class UsUser implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id" , type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 成员userid
     */
    @TableField(value = "source_userid")
    private String sourceUserid;

    /**
     * 成员名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 成员别名
     */
    @TableField(value = "alias")
    private String alias;

    /**
     * 手机号码
     */
    @TableField(value = "mobile")
    private String mobile;

    /**
     * 职务信息
     */
    @TableField(value = "position")
    private String position;

    /**
     * 性别。1表示男性，2表示女性
     */
    @TableField(value = "gender")
    private String gender;

    /**
     * 邮箱
     */
    @TableField(value = "email")
    private String email;

    /**
     * 企业邮箱
     */
    @TableField(value = "biz_mail")
    private String bizMail;

    /**
     * 座机
     */
    @TableField(value = "telephone")
    private String telephone;

    /**
     * 直属上级
     */
    @TableField(value = "direct_leader")
    private String directLeader;

    /**
     * 成员头像的mediaid
     */
    @TableField(value = "avatar_mediaid")
    private String avatarMediaid;

    /**
     * 启用/禁用成员。1表示启用成员，0表示禁用成员
     */
    @TableField(value = "enable")
    private Integer enable;

    /**
     * 自定义字段
     */
    @TableField(value = "extattr")
    private String extattr;

    /**
     * 是否邀请该成员使用企业微信，默认值为true
     */
    @TableField(value = "to_invite")
    private Integer toInvite;

    /**
     * 对外职务
     */
    @TableField(value = "external_position")
    private String externalPosition;

    /**
     * 成员对外属性
     */
    @TableField(value = "external_profile")
    private String externalProfile;

    /**
     * 地址。长度最大128个字符
     */
    @TableField(value = "address")
    private String address;

    /**
     * 主部门
     */
    @TableField(value = "main_department")
    private Integer mainDepartment;

    /**
     * 数据状态  1新增、2修改 、3删除
     */
    @TableField(value = "data_status")
    private Integer dataStatus;

    /**
     * 同步状态  0初始值 1同步中 2同步成功 3同步失败
     */
    @TableField(value = "sync_status")
    private Integer syncStatus;

    /**
     * 当前版本
     */
    @TableField(value = "version")
    private Integer version;

    /**
     * 删除标记  0未删除 1已删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 成员microsoft AAD系统提供的uuid
     */
    @TableField(value = "aad_id")
    private String aadId;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private Date gmtModified;

    /**
     * 企微成员userid
     */
    @TableField(value = "target_userid")
    private String targetUserid;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}