package com.wshoto.jiali.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 同步通知配置
 * @TableName us_notify_config
 */
@TableName(value ="us_notify_config")
@Data
public class UsNotifyConfig implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id" , type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 通知类型  1群消息 2邮件
     */
    @TableField(value = "notify_type")
    private Integer notifyType;

    /**
     * 是否开启 1开启 0关闭
     */
    @TableField(value = "is_open")
    private Integer isOpen;

    /**
     * 邮箱
     */
    @TableField(value = "mail")
    private String mail;

    /**
     * 用户名
     */
    @TableField(value = "username")
    private String username;

    /**
     * 密码
     */
    @TableField(value = "password")
    private String password;

    /**
     * SMTP 服务器域名
     */
    @TableField(value = "host")
    private String host;

    /**
     * SMTP 服务器端口
     */
    @TableField(value = "port")
    private Long port;

    /**
     * 机器人密钥key
     */
    @TableField(value = "robot_key")
    private String robotKey;

    /**
     * 发送人或@人
     */
    @TableField(value = "to_user")
    private String toUser;

    /**
     * 当前版本
     */
    @TableField(value = "version")
    private Integer version;

    /**
     * 删除标记  0未删除 1已删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private Date gmtModified;

    @TableField("ssl_enable")
    private Boolean sslEnable;

    @TableField("time_out")
    private Integer timeOut;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}