package com.wshoto.jiali.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 部门成员关系数据
 * @TableName us_department_user
 */
@TableName(value ="us_department_user")
@Data
public class UsDepartmentUser implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id" , type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 成员microsoft AAD系统提供的uuid
     */
    @TableField(value = "aad_id")
    private String aadId;

    /**
     * 
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 来源部门id
     */
    @TableField(value = "source_dept_id")
    private String sourceDeptId;

    /**
     * 目标系统(企微)部门id
     */
    @TableField(value = "target_dept_id")
    private String targetDeptId;

    /**
     * 目标系统成员id
     */
    @TableField(value = "tartget_userid")
    private String tartgetUserid;

    /**
     * 是否部门主管 1是 0否
     */
    @TableField(value = "is_dept_leader")
    private Integer isDeptLeader;

    /**
     * 部门中的排序
     */
    @TableField(value = "dept_order")
    private Integer deptOrder;

    /**
     * 当前版本
     */
    @TableField(value = "version")
    private Integer version;

    /**
     * 删除标记  0未删除 1已删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private Date gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}