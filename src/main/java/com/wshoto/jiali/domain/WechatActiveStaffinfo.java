package com.wshoto.jiali.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName wechat_active_staffinfo
 */
@TableName(value ="wechat_active_staffinfo")
@Data
public class WechatActiveStaffinfo implements Serializable {
    /**
     * 
     */
    @TableField(value = "staff_english_last_name")
    private String staffEnglishLastName;

    /**
     * 
     */
    @TableField(value = "staff_english_first_name")
    private String staffEnglishFirstName;

    /**
     * 
     */
    @TableField(value = "staff_chinese_last_name")
    private String staffChineseLastName;

    /**
     * 
     */
    @TableField(value = "staff_chinese_first_name")
    private String staffChineseFirstName;

    /**
     * 
     */
    @TableField(value = "preferred_name")
    private String preferredName;

    /**
     * 
     */
    @TableField(value = "formal_name_personal_info")
    private String formalNamePersonalInfo;

    /**
     * 
     */
    @TableField(value = "staff_no")
    private String staffNo;

    /**
     * 
     */
    @TableField(value = "english_title")
    private String englishTitle;

    /**
     * 
     */
    @TableField(value = "chinese_title")
    private String chineseTitle;

    /**
     * 
     */
    @TableField(value = "mobile_area_code")
    private String mobileAreaCode;

    /**
     * 
     */
    @TableField(value = "mobile_no")
    private String mobileNo;

    /**
     * 
     */
    @TableField(value = "email_address_kpl")
    private String emailAddressKpl;

    /**
     * 
     */
    @TableField(value = "email_address_personal")
    private String emailAddressPersonal;

    /**
     * 
     */
    @TableField(value = "country_region")
    private String countryRegion;

    /**
     * 
     */
    @TableField(value = "group_code")
    private String groupCode;

    /**
     * 
     */
    @TableField(value = "division_code")
    private String divisionCode;

    /**
     * 
     */
    @TableField(value = "business_group_code")
    private String businessGroupCode;

    /**
     * 
     */
    @TableField(value = "business_unit_code")
    private String businessUnitCode;

    /**
     * 
     */
    @TableField(value = "project_code")
    private String projectCode;

    /**
     * 
     */
    @TableField(value = "legal_entity_code")
    private String legalEntityCode;

    /**
     * 
     */
    @TableField(value = "function_code")
    private String functionCode;

    /**
     * 
     */
    @TableField(value = "department")
    private String department;

    /**
     * 
     */
    @TableField(value = "phone_lastmodifieddatetime")
    private Date phoneLastmodifieddatetime;

    /**
     * 
     */
    @TableField(value = "email_lastmodifieddatetime")
    private Date emailLastmodifieddatetime;

    /**
     * 
     */
    @TableField(value = "job_lastmodifieddatetime")
    private Date jobLastmodifieddatetime;

    /**
     * 
     */
    @TableField(value = "personinfo_lastmodifiedatetime")
    private Date personinfoLastmodifiedatetime;

    /**
     * 
     */
    @TableField(value = "hire_date")
    private Date hireDate;

    /**
     * 
     */
    @TableField(value = "system_sid")
    private String systemSid;

    /**
     * 
     */
    @TableField(value = "business_phone")
    private String businessPhone;

    /**
     * 
     */
    @TableField(value = "staff_english_name")
    private String staffEnglishName;

    /**
     * 
     */
    @TableField(value = "staff_chinese_name")
    private String staffChineseName;

    /**
     * 
     */
    @TableField(value = "ad_username")
    private String adUsername;

    /**
     * 
     */
    @TableField(value = "gender")
    private String gender;

    /**
     * 
     */
    @TableField(value = "mobile_countrycode")
    private String mobileCountrycode;

    /**
     * 
     */
    @TableField(value = "supervisor_staffno")
    private String supervisorStaffno;

    /**
     * 
     */
    @TableField(value = "supervisor_name")
    private String supervisorName;

    /**
     * 
     */
    @TableField(value = "supervisor_ad_username")
    private String supervisorAdUsername;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}