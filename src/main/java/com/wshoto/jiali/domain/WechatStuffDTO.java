package com.wshoto.jiali.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * WechatStuffDTO实体类
 *
 * <AUTHOR>
 * Created Date - 2025-3-19
 */
@Data
@TableName("tb_wechat_stuff")
public class WechatStuffDTO {

    @TableField(value = "aad_id")
    private String aadId;

    @TableField(value = "deleted")
    private Integer deleted;

    @TableField(value = "enabled")
    private Integer enabled;

    @TableField(value = "staff_no")
    private String staffNo;

    @TableField(value = "english_title")
    private String englishTitle;

    @TableField(value = "chinese_title")
    private String chineseTitle;

    @TableField(value = "mobile_phone")
    private String mobilePhone;

    @TableField(value = "email_address_kpl")
    private String emailAddressKpl;

    @TableField(value = "department")
    private String department;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(value = "business_phone")
    private String businessPhone;

    @TableField(value = "staff_english_name")
    private String staffEnglishName;

    @TableField(value = "staff_chinese_name")
    private String staffChineseName;

    @TableField(value = "ad_username")
    private String adUsername;

    @TableField(value = "gender")
    private String gender;

    @TableField(value = "supervisor_staffno")
    private String supervisorStaffno;

    @TableField(value = "supervisor_aad_id")
    private String supervisorAadId;

    @TableField(value = "supervisor_ad_username")
    private String supervisorAdUsername;

    public boolean isEnabled() {
        return this.enabled != null && this.enabled == 1;
    }

}