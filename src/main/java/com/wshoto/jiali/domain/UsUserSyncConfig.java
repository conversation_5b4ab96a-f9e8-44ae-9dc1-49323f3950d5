package com.wshoto.jiali.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 成员信息同步配置
 * @TableName us_user_sync_config
 */
@TableName(value ="us_user_sync_config")
@Data
public class UsUserSyncConfig implements Serializable {
    /**
     * 主键
     */
    @TableField(value = "id")
    private Long id;

    /**
     * 
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 同步类型 1新增 2修改
     */
    @TableField(value = "sync_type")
    private Integer syncType;

    /**
     * 同步字段
     */
    @TableField(value = "sync_column")
    private String syncColumn;

    /**
     * 是否必须  1是 0否
     */
    @TableField(value = "is_must")
    private Integer isMust;

    /**
     * 是否忽略 1是 0否
     */
    @TableField(value = "is_ignore")
    private Integer isIgnore;

    /**
     * 当前版本
     */
    @TableField(value = "version")
    private Integer version;

    /**
     * 删除标记  0未删除 1已删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private Date gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}