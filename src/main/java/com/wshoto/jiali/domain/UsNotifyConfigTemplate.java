package com.wshoto.jiali.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName us_notify_config_template
 */
@TableName(value ="us_notify_config_template")
@Data
public class UsNotifyConfigTemplate implements Serializable {
    /**
     * 编号
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 模板名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 模板编码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 异常通知配置id
     */
    @TableField(value = "config_id")
    private Long configId;

    /**
     * 发送人名称
     */
    @TableField(value = "nickname")
    private String nickname;

    /**
     * 模板标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 模板内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 参数数组
     */
    @TableField(value = "params")
    private String params;

    /**
     * 开启状态
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 当前版本
     */
    @TableField(value = "version")
    private Integer version;

    /**
     * 删除标记  0未删除 1已删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private Date gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}