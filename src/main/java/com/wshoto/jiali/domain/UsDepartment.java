package com.wshoto.jiali.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 部门同步表
 * @TableName us_department
 */
@TableName(value ="us_department")
@Data
public class UsDepartment implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id" , type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 来源系统部门id
     */
    @TableField(value = "source_dept_id")
    private String sourceDeptId;

    /**
     * 来源系统部门上级id
     */
    @TableField(value = "source_dept_parent_id")
    private String sourceDeptParentId;

    /**
     * 企微部门id
     */
    @TableField(value = "target_dept_id")
    private String targetDeptId;

    /**
     * 目标系统部门上级id
     */
    @TableField(value = "target_dept_parent_id")
    private String targetDeptParentId;

    /**
     * 部门名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 部门英文名称
     */
    @TableField(value = "name_en")
    private String nameEn;

    /**
     * 排序
     */
    @TableField(value = "\"order\"")
    private Integer order;

    /**
     * 数据状态  1新增、2修改 、3删除
     */
    @TableField(value = "data_status")
    private Integer dataStatus;

    /**
     * 同步状态  0初始值 1同步中 2同步成功 3同步失败
     */
    @TableField(value = "sync_status")
    private Integer syncStatus;

    /**
     * 当前版本
     */
    @TableField(value = "version")
    private Integer version;

    /**
     * 删除标记  0未删除 1已删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private Date gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}