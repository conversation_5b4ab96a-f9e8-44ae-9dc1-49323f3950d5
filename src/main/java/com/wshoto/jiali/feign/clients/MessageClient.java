package com.wshoto.jiali.feign.clients;

import com.wshoto.jiali.config.FeignConfig;
import com.wshoto.jiali.domain.EmailSendCommandVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * unified-messaging-service FeignClient
 *
 * <AUTHOR>
 * @date 2025-2-20
 */
@FeignClient(name = "unifiedMessagingService", configuration = FeignConfig.class
        , url = "${email.services.unified-messaging}")
public interface MessageClient {

    @PostMapping(value = "/email/alicloud/send")
    String sendWithReply(EmailSendCommandVo command);

}
