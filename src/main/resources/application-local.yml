server:
  port: 8080
spring:
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        db_qa_wecom_datasync:   # 数据库1
          driver-class-name: org.postgresql.Driver
          url: **************************************************************************************************************************************************
          username: qa_wecom
          password: YSJ12OuzKkhLQ8nW2ijXRrJWKtAEg9PM
        db_qa_wecom:   # 数据库2
          driver-class-name: org.postgresql.Driver
          url: *****************************************************************************************************************************************
          username: qa_wecom
          password: YSJ12OuzKkhLQ8nW2ijXRrJWKtAEg9PM

  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER

distributed:
  jobs:
    enabled: false

mybatis-plus:
  configuration:
    # 开启sql日志
    #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    #关闭sql日志
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

logging:
  config: classpath:log4j2.xml

email:
  send-to-list: <EMAIL>,<EMAIL>
  services:
    unified-messaging: https://dev-kip-service-internal.kerryonvip.com/unified-messaging-service

jiali:
  tenantid: ww4aaccf11cd9ae333
  mail: <EMAIL>
  username: <EMAIL>
  password: K1rry+Platform#2022!
  host: smtp.mailrelay.cn
  port: 465
  whiteDeptL2: KPCND
  whiteDept: KPPJ-HKPJ,KPPJ-HKPpppp,KPDP-TI-TISHKPCPMNM
  externalPosition: 员工
  extattrName: 职务(中)
  externalCorpName: 嘉里（中国）项目管理上海分公司