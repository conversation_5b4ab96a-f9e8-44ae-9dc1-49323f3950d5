<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
</head>
<body
        style="word-wrap: break-word; -webkit-nbsp-mode: space; -webkit-line-break: after-white-space; color: rgb(0, 0, 0); font-family: Calibri, sans-serif; font-size: 14px;"
>
<div style="font-size: 14px;">
    <div
            class="wiki-content"
            id="main-content"
            style="margin: 0px; padding: 0px; color: rgb(51, 51, 51); font-family: Arial, sans-serif; line-height: 20px; widows: 1; background-color: rgb(255, 255, 255);"
    >
        <h1
                id="Wshot-Ka-Jiali-Service-EmailTemplate"
                style="margin: 30px 0px 0px; padding: 0px; font-size: 24px; font-weight: normal; line-height: 1.25; border-bottom-color: rgb(204, 204, 204);"
        >
        </h1>
        <hr
                style="border-top-width: 0px; border-right-width: 0px; border-left-width: 0px; border-bottom-style: solid; border-bottom-color: rgb(204, 204, 204);"
        />
        <p style="margin: 10px 0px 0px; padding: 0px;">&nbsp;</p>
        <div
                class="table-wrap"
                style="margin: 10px 0px 0px; padding: 0px; overflow-x: auto;"
        >
            <table
                    class="confluenceTable"
                    style="border-collapse: collapse; margin: 0px; overflow-x: auto;"
            >
                <tbody>
                <tr>
                    <th
                            class="confluenceTh"
                            style="border: 1px solid rgb(221, 221, 221); padding: 7px 10px; vertical-align: top; color: rgb(51, 51, 51); background-color: rgb(240, 240, 240);"
                    >
                        <p style="margin: 0px; padding: 0px;">
                            Execution Result
                        </p>
                    </th>
                    <td
                            class="confluenceTd"
                            style="border: 1px solid rgb(221, 221, 221); padding: 7px 10px; vertical-align: top;"
                    >
                        <p style="margin: 0px; padding: 0px;" th:utext="${executionResult}">
                            &nbsp;
                        </p>
                    </td>
                </tr>
                <tr>
                    <th
                            class="confluenceTh"
                            style="border: 1px solid rgb(221, 221, 221); padding: 7px 10px; vertical-align: top; color: rgb(51, 51, 51); background-color: rgb(240, 240, 240);"
                    >
                        <p style="margin: 0px; padding: 0px;">
                            Error Happening Time
                        </p>
                    </th>
                    <td
                            class="confluenceTd"
                            style="border: 1px solid rgb(221, 221, 221); padding: 7px 10px; vertical-align: top;"
                    >
                        <p style="margin: 0px; padding: 0px;" th:utext="${errorHappeningTime}">
                            &nbsp;
                        </p>
                    </td>
                </tr>
                <tr>
                    <th
                            class="confluenceTh"
                            style="border: 1px solid rgb(221, 221, 221); padding: 7px 10px; vertical-align: top; color: rgb(51, 51, 51); background-color: rgb(240, 240, 240);"
                    >
                        <p style="margin: 0px; padding: 0px;">
                            Error Message
                        </p>
                    </th>
                    <td
                            class="confluenceTd"
                            style="border: 1px solid rgb(221, 221, 221); padding: 7px 10px; vertical-align: top;"
                    >
                        <p style="margin: 0px; padding: 0px;" th:utext="${errorMessage}">
                            &nbsp;
                        </p>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        #end #end
    </div>
    <div
            id="likes-and-labels-container"
            style="margin: 10px 0px; padding: 10px 0px; overflow: hidden; font-size: 13px; clear: both; color: rgb(51, 51, 51); font-family: Arial, sans-serif; widows: 1; background-color: rgb(255, 255, 255);"
    >
        <div
                id="likes-section"
                style="margin: 0px; padding: 0px; float: left; width: 501.109px;"
        ></div>
    </div>
</div>
<div></div>
</body>
</html>
