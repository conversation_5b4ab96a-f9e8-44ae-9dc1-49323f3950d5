
CREATE TABLE public.us_department (
	id int8 NOT NULL, 
	source_dept_id varchar(200) NULL, 
	source_dept_parent_id varchar(200) NULL, 
	target_dept_id varchar(50) NULL, 
	target_dept_parent_id varchar(50) NULL, 
	"name" varchar(100) NOT NULL, 
	name_en varchar(100) NULL, 
	"order" int4 NULL DEFAULT 0, 
	data_status int4 NULL, 
	sync_status int4 NULL DEFAULT 0, 
	"version" int4 NOT NULL DEFAULT 0, 
	deleted int2 NOT NULL DEFAULT 0, 
	gmt_create timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, 
	gmt_modified timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, 
	tenant_id varchar(50) NULL, 
	CONSTRAINT us_department_pk PRIMARY KEY (id)
);
COMMENT ON TABLE public.us_department IS '部门同步表';

COMMENT ON COLUMN public.us_department.id IS '主键';
COMMENT ON COLUMN public.us_department.source_dept_id IS '来源系统部门id';
COMMENT ON COLUMN public.us_department.source_dept_parent_id IS '来源系统部门上级id';
COMMENT ON COLUMN public.us_department.target_dept_id IS '企微部门id';
COMMENT ON COLUMN public.us_department.target_dept_parent_id IS '目标系统部门上级id';
COMMENT ON COLUMN public.us_department."name" IS '部门名称';
COMMENT ON COLUMN public.us_department.name_en IS '部门英文名称';
COMMENT ON COLUMN public.us_department."order" IS '排序';
COMMENT ON COLUMN public.us_department.data_status IS '数据状态  1新增、2修改 、3删除';
COMMENT ON COLUMN public.us_department.sync_status IS '同步状态  0初始值 1同步中 2同步成功 3同步失败';
COMMENT ON COLUMN public.us_department."version" IS '当前版本';
COMMENT ON COLUMN public.us_department.deleted IS '删除标记  0未删除 1已删除';
COMMENT ON COLUMN public.us_department.gmt_create IS '创建时间';
COMMENT ON COLUMN public.us_department.gmt_modified IS '修改时间';
COMMENT ON COLUMN public.us_department.tenant_id IS '租户id';


CREATE TABLE public.us_user (
	id int8 NOT NULL, 
	source_userid varchar(64) NOT NULL, 
	"name" varchar(64) NULL, 
	alias varchar(64) NULL, 
	mobile varchar(20) NULL, 
	"position" varchar(128) NULL, 
	gender varchar(20) NULL, 
	biz_mail varchar(64) NULL, 
	telephone varchar(32) NULL, 
	direct_leader varchar(64) NULL, 
	avatar_mediaid varchar(50) NULL, 
	"enable" int2 NULL, 
	extattr varchar NULL, 
	to_invite int2 NULL DEFAULT 1, 
	external_position varchar(12) NULL, 
	external_profile varchar NULL, 
	address varchar(128) NULL, 
	main_department int4 NULL, 
	data_status int4 NULL, 
	sync_status int4 NULL DEFAULT 0, 
	"version" int4 NOT NULL DEFAULT 0, 
	deleted int2 NOT NULL DEFAULT 0, 
	gmt_create timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, 
	gmt_modified timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, 
	target_userid varchar(64) NOT NULL, 
	tenant_id varchar(50) NULL, 
	email varchar(64) NULL, 
	CONSTRAINT us_user_pk PRIMARY KEY (id)
);
COMMENT ON TABLE public.us_user IS '成员同步表';

COMMENT ON COLUMN public.us_user.id IS '主键';
COMMENT ON COLUMN public.us_user.source_userid IS '成员userid';
COMMENT ON COLUMN public.us_user."name" IS '成员名称';
COMMENT ON COLUMN public.us_user.alias IS '成员别名';
COMMENT ON COLUMN public.us_user.mobile IS '手机号码';
COMMENT ON COLUMN public.us_user."position" IS '职务信息';
COMMENT ON COLUMN public.us_user.gender IS '性别。1表示男性，2表示女性';
COMMENT ON COLUMN public.us_user.biz_mail IS '企业邮箱';
COMMENT ON COLUMN public.us_user.telephone IS '座机';
COMMENT ON COLUMN public.us_user.direct_leader IS '直属上级';
COMMENT ON COLUMN public.us_user.avatar_mediaid IS '成员头像的mediaid';
COMMENT ON COLUMN public.us_user."enable" IS '启用/禁用成员。1表示启用成员，0表示禁用成员';
COMMENT ON COLUMN public.us_user.extattr IS '自定义字段';
COMMENT ON COLUMN public.us_user.to_invite IS '是否邀请该成员使用企业微信，默认值为true';
COMMENT ON COLUMN public.us_user.external_position IS '对外职务';
COMMENT ON COLUMN public.us_user.external_profile IS '成员对外属性';
COMMENT ON COLUMN public.us_user.address IS '地址。长度最大128个字符';
COMMENT ON COLUMN public.us_user.main_department IS '主部门';
COMMENT ON COLUMN public.us_user.data_status IS '数据状态  1新增、2修改 、3删除';
COMMENT ON COLUMN public.us_user.sync_status IS '同步状态  0初始值 1同步中 2同步成功 3同步失败';
COMMENT ON COLUMN public.us_user."version" IS '当前版本';
COMMENT ON COLUMN public.us_user.deleted IS '删除标记  0未删除 1已删除';
COMMENT ON COLUMN public.us_user.gmt_create IS '创建时间';
COMMENT ON COLUMN public.us_user.gmt_modified IS '修改时间';
COMMENT ON COLUMN public.us_user.target_userid IS '企微成员userid';
COMMENT ON COLUMN public.us_user.tenant_id IS '租户id';
COMMENT ON COLUMN public.us_user.email IS '邮箱';


CREATE TABLE public.us_user_sync_config (
	id int8 NOT NULL, 
	sync_type int4 NULL, 
	sync_column varchar(50) NULL, 
	is_must int2 NULL, 
	is_ignore int2 NULL, 
	"version" int4 NOT NULL DEFAULT 0, 
	deleted int2 NOT NULL DEFAULT 0, 
	gmt_create timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, 
	gmt_modified timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, 
	tenant_id varchar(50) NULL 
);
COMMENT ON TABLE public.us_user_sync_config IS '成员信息同步配置';

COMMENT ON COLUMN public.us_user_sync_config.id IS '主键';
COMMENT ON COLUMN public.us_user_sync_config.sync_type IS '同步类型 1新增 2修改';
COMMENT ON COLUMN public.us_user_sync_config.sync_column IS '同步字段';
COMMENT ON COLUMN public.us_user_sync_config.is_must IS '是否必须  1是 0否';
COMMENT ON COLUMN public.us_user_sync_config.is_ignore IS '是否忽略 1是 0否';
COMMENT ON COLUMN public.us_user_sync_config."version" IS '当前版本';
COMMENT ON COLUMN public.us_user_sync_config.deleted IS '删除标记  0未删除 1已删除';
COMMENT ON COLUMN public.us_user_sync_config.gmt_create IS '创建时间';
COMMENT ON COLUMN public.us_user_sync_config.gmt_modified IS '修改时间';
COMMENT ON COLUMN public.us_user_sync_config.tenant_id IS '租户id';

CREATE TABLE public.us_sync_log (
	id int8 NOT NULL, 
	url varchar(100) NULL, 
	request varchar NULL, 
	"result" varchar NULL, 
	status varchar(50) NULL, 
	"version" int4 NOT NULL DEFAULT 0, 
	deleted int2 NOT NULL DEFAULT 0, 
	gmt_create timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, 
	gmt_modified timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, 
	tenant_id varchar(50) NULL, 
	batch_no varchar(50) NULL 
);
COMMENT ON TABLE public.us_sync_log IS '同步日志';

COMMENT ON COLUMN public.us_sync_log.id IS '主键';
COMMENT ON COLUMN public.us_sync_log.url IS '接口地址';
COMMENT ON COLUMN public.us_sync_log.request IS '参数';
COMMENT ON COLUMN public.us_sync_log."result" IS '返回结果';
COMMENT ON COLUMN public.us_sync_log.status IS '接口状态';
COMMENT ON COLUMN public.us_sync_log."version" IS '当前版本';
COMMENT ON COLUMN public.us_sync_log.deleted IS '删除标记  0未删除 1已删除';
COMMENT ON COLUMN public.us_sync_log.gmt_create IS '创建时间';
COMMENT ON COLUMN public.us_sync_log.gmt_modified IS '修改时间';
COMMENT ON COLUMN public.us_sync_log.tenant_id IS '租户id';
COMMENT ON COLUMN public.us_sync_log.batch_no IS '批次号';


CREATE TABLE public.us_notify_config (
	id int8 NOT NULL, 
	notify_type int4 NULL, 
	is_open int2 NULL, 
	mail varchar(255) NOT NULL, 
	username varchar(255) NOT NULL, 
	"password" varchar(255) NOT NULL, 
	host varchar(255) NOT NULL, 
	port int8 NOT NULL, 
	robot_key varchar(200) NULL, 
	"version" int4 NOT NULL DEFAULT 0, 
	deleted int2 NOT NULL DEFAULT 0, 
	gmt_create timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, 
	gmt_modified timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, 
	to_user varchar NULL, 
	tenant_id varchar(50) NULL, 
	CONSTRAINT us_notify_config_pk PRIMARY KEY (id)
);
COMMENT ON TABLE public.us_notify_config IS '同步通知配置';

COMMENT ON COLUMN public.us_notify_config.id IS '主键';
COMMENT ON COLUMN public.us_notify_config.notify_type IS '通知类型  1群消息 2邮件';
COMMENT ON COLUMN public.us_notify_config.is_open IS '是否开启 1开启 0关闭';
COMMENT ON COLUMN public.us_notify_config.mail IS '邮箱';
COMMENT ON COLUMN public.us_notify_config.username IS '用户名';
COMMENT ON COLUMN public.us_notify_config."password" IS '密码';
COMMENT ON COLUMN public.us_notify_config.host IS 'SMTP 服务器域名';
COMMENT ON COLUMN public.us_notify_config.port IS 'SMTP 服务器端口';
COMMENT ON COLUMN public.us_notify_config.robot_key IS '机器人密钥key';
COMMENT ON COLUMN public.us_notify_config."version" IS '当前版本';
COMMENT ON COLUMN public.us_notify_config.deleted IS '删除标记  0未删除 1已删除';
COMMENT ON COLUMN public.us_notify_config.gmt_create IS '创建时间';
COMMENT ON COLUMN public.us_notify_config.gmt_modified IS '修改时间';
COMMENT ON COLUMN public.us_notify_config.to_user IS '发送人或@人以,分割';
COMMENT ON COLUMN public.us_notify_config.tenant_id IS '租户id';


CREATE TABLE public.us_notify_config_template (
	id int8 NOT NULL, 
	"name" varchar(64) NOT NULL, 
	code varchar(64) NOT NULL, 
	config_id int8 NOT NULL, 
	nickname varchar(255) NULL, 
	title varchar(255) NOT NULL, 
	"content" varchar(10240) NOT NULL, 
	params varchar(255) NULL, 
	status int2 NOT NULL, 
	remark varchar(255) NULL, 
	"version" int4 NOT NULL DEFAULT 0, 
	deleted int2 NOT NULL DEFAULT 0, 
	gmt_create timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, 
	gmt_modified timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, 
	tenant_id varchar(50) NULL, 
	CONSTRAINT us_notify_config_template_pk PRIMARY KEY (id)
);

COMMENT ON COLUMN public.us_notify_config_template.id IS '编号';
COMMENT ON COLUMN public.us_notify_config_template."name" IS '模板名称';
COMMENT ON COLUMN public.us_notify_config_template.code IS '模板编码';
COMMENT ON COLUMN public.us_notify_config_template.config_id IS '异常通知配置id';
COMMENT ON COLUMN public.us_notify_config_template.nickname IS '发送人名称';
COMMENT ON COLUMN public.us_notify_config_template.title IS '模板标题';
COMMENT ON COLUMN public.us_notify_config_template."content" IS '模板内容';
COMMENT ON COLUMN public.us_notify_config_template.params IS '参数数组';
COMMENT ON COLUMN public.us_notify_config_template.status IS '开启状态';
COMMENT ON COLUMN public.us_notify_config_template.remark IS '备注';
COMMENT ON COLUMN public.us_notify_config_template."version" IS '当前版本';
COMMENT ON COLUMN public.us_notify_config_template.deleted IS '删除标记  0未删除 1已删除';
COMMENT ON COLUMN public.us_notify_config_template.gmt_create IS '创建时间';
COMMENT ON COLUMN public.us_notify_config_template.gmt_modified IS '修改时间';
COMMENT ON COLUMN public.us_notify_config_template.tenant_id IS '租户id';


CREATE TABLE public.us_department_user (
	id int8 NOT NULL, 
	source_dept_id varchar(100) NULL, 
	target_dept_id varchar(100) NULL, 
	tartget_userid varchar(64) NULL, 
	is_dept_leader int2 NULL, 
	dept_order int4 NULL, 
	"version" int4 NOT NULL DEFAULT 0, 
	deleted int2 NOT NULL DEFAULT 0, 
	gmt_create timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, 
	gmt_modified timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, 
	tenant_id varchar(50) NULL, 
	CONSTRAINT us_department_user_pk PRIMARY KEY (id)
);
COMMENT ON TABLE public.us_department_user IS '部门成员关系数据';

COMMENT ON COLUMN public.us_department_user.id IS '主键';
COMMENT ON COLUMN public.us_department_user.source_dept_id IS '来源部门id';
COMMENT ON COLUMN public.us_department_user.target_dept_id IS '目标系统(企微)部门id';
COMMENT ON COLUMN public.us_department_user.tartget_userid IS '目标系统成员id';
COMMENT ON COLUMN public.us_department_user.is_dept_leader IS '是否部门主管 1是 0否';
COMMENT ON COLUMN public.us_department_user.dept_order IS '部门中的排序';
COMMENT ON COLUMN public.us_department_user."version" IS '当前版本';
COMMENT ON COLUMN public.us_department_user.deleted IS '删除标记  0未删除 1已删除';
COMMENT ON COLUMN public.us_department_user.gmt_create IS '创建时间';
COMMENT ON COLUMN public.us_department_user.gmt_modified IS '修改时间';
COMMENT ON COLUMN public.us_department_user.tenant_id IS '租户id';


CREATE TABLE public.us_job (
	id int8 NOT NULL, 
	job_id varchar(100) NULL, 
	tenant_id varchar(50) NULL, 
	cron varchar(50) NULL, 
	remark varchar(200) NULL, 
	"version" int4 NOT NULL DEFAULT 0, 
	deleted int2 NOT NULL DEFAULT 0, 
	gmt_create timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, 
	gmt_modified timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP 
);
COMMENT ON TABLE public.us_job IS '定时任务配置';

COMMENT ON COLUMN public.us_job.id IS '主键';
COMMENT ON COLUMN public.us_job.job_id IS '任务标识';
COMMENT ON COLUMN public.us_job.tenant_id IS '租户id';
COMMENT ON COLUMN public.us_job.cron IS '定时cron配置';
COMMENT ON COLUMN public.us_job.remark IS '任务备注';
COMMENT ON COLUMN public.us_job."version" IS '当前版本';
COMMENT ON COLUMN public.us_job.deleted IS '删除标记  0未删除 1已删除';
COMMENT ON COLUMN public.us_job.gmt_create IS '创建时间';
COMMENT ON COLUMN public.us_job.gmt_modified IS '修改时间';
