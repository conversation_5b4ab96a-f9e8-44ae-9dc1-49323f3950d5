-- auto-generated definition
create table jiali_us_sync_log
(
    id             bigint,
    user_id        varchar(64),
    parent_dept_id varchar(128),
    dept_id        varchar(128),
    error_type     integer,
    error_desc     text,
    error_json     text,
    create_time    timestamp,
    update_time    timestamp
);

comment on table jiali_us_sync_log is '数据清洗入库异常日志';

comment on column jiali_us_sync_log.error_desc is '错误信息';

comment on column jiali_us_sync_log.error_json is '错误的json信息';

comment on column jiali_us_sync_log.create_time is '创建时间';

comment on column jiali_us_sync_log.update_time is '修改时间';

alter table jiali_us_sync_log
    owner to dbadmin;


