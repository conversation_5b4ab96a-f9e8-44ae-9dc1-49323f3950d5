INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(1, 1, 'userid', 1, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(2, 1, 'name', 1, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(3, 1, 'alias', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(4, 1, 'mobile', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(5, 1, 'department', 1, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(6, 1, 'order', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(7, 1, 'position', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(8, 1, 'gender', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(9, 1, 'email', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(10, 1, 'biz_mail', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(11, 1, 'telephone', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(12, 1, 'is_leader_in_dept', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(13, 1, 'direct_leader', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(14, 1, 'avatar_mediaid', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(15, 1, 'enable', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(16, 1, 'extattr', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(17, 1, 'to_invite', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(18, 1, 'external_profile', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(19, 1, 'external_position', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(20, 1, 'address', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(21, 1, 'main_department', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(22, 2, 'userid', 1, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(23, 2, 'name', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(24, 2, 'alias', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(25, 2, 'mobile', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(26, 2, 'department', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(27, 2, 'order', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(28, 2, 'position', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(29, 2, 'gender', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(30, 2, 'email', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(31, 2, 'biz_mail', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(32, 2, 'telephone', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(33, 2, 'is_leader_in_dept', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(34, 2, 'direct_leader', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(35, 2, 'avatar_mediaid', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(36, 2, 'enable', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(37, 2, 'extattr', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(38, 2, 'to_invite', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(39, 2, 'external_profile', 0, 1, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(40, 2, 'external_position', 0, 1, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(41, 2, 'address', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');
INSERT INTO public.us_user_sync_config (id, sync_type, sync_column, is_must, is_ignore, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(42, 2, 'main_department', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'wwd4ffda3aa66648da');

INSERT INTO public.us_job (id, job_id, tenant_id, cron, remark, "version", deleted, gmt_create, gmt_modified) VALUES(1, 'QW_USER_SYNC_JOB', 'wwd4ffda3aa66648da', '0 0 0 * * ?', NULL, 0, 0, '2024-03-08 14:30:01.895', '2024-03-08 14:30:01.895');
INSERT INTO public.us_notify_config (id, notify_type, is_open, mail, username, password, host, port, robot_key, version, deleted, gmt_create, gmt_modified, to_user, tenant_id) VALUES (1, 2, 1, '<EMAIL>', '<EMAIL>', 'K1rry+Platform#2022!', 'smtp.mailrelay.cn', 465, null, 0, 0, '2024-02-19 11:43:11.754000', '2024-02-19 11:43:11.754000', '<EMAIL>', 'ww4aaccf11cd9ae333');
INSERT INTO public.us_notify_config_template (id, "name", code, config_id, nickname, title, "content", params, status, remark, "version", deleted, gmt_create, gmt_modified, tenant_id) VALUES(1, '通讯录同步结果邮件模版', 'USER_SYNC_EMAIL_TEMPLATE', 1, NULL, '通讯录同步结果', '本次通讯录同步批次号{},同步成功部门数量{},同步失败部门数量{},同步成员成功数量{},同步成员失败数量{},详细信息查看同步记录', NULL, 1, NULL, 0, 0, '2024-02-19 11:45:21.490', '2024-02-19 11:45:21.490', NULL);


ALTER TABLE public.us_notify_config ADD ssl_enable bool NOT NULL DEFAULT false;
COMMENT ON COLUMN public.us_notify_config.ssl_enable IS '是否开启 SSL';

ALTER TABLE public.us_notify_config ADD time_out int4 NULL;
COMMENT ON COLUMN public.us_notify_config.time_out IS '超时时间，单位毫秒';

update us_notify_config set ssl_enable = true where notify_type = 2;