<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wshoto.jiali.mapper.UsUserMapper">


	<!-- 更新成员信息 -->
	<update id="updateUsUser" parameterType="com.wshoto.jiali.domain.UsUser">
		UPDATE us_user
		<set>
			name = #{name},
			alias = #{alias},
			mobile = #{mobile},
			position = #{position},
			gender = #{gender},
			email = #{email},
			biz_mail = #{bizMail},
			telephone = #{telephone},
			direct_leader = #{directLeader},
			avatar_mediaid = #{avatarMediaid},
			enable = #{enable},
			extattr = #{extattr},
			to_invite = #{toInvite},
			external_position = #{externalPosition},
			external_profile = #{externalProfile},
			address = #{address},
			main_department = #{mainDepartment},
			data_status = #{dataStatus},
			sync_status = #{syncStatus},
			version = #{version},
			deleted = #{deleted},
			gmt_create = #{gmtCreate},
			gmt_modified = #{gmtModified},
			target_userid = #{targetUserid}
		</set>
		WHERE id = #{id}
	</update>


</mapper>
