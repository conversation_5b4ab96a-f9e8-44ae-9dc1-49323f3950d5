<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wshoto.jiali.mapper.JialiUsSyncLogMapper">

    <resultMap id="BaseResultMap" type="com.wshoto.jiali.domain.JialiUsSyncLog">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="parentDeptId" column="parent_dept_id" jdbcType="VARCHAR"/>
            <result property="deptId" column="dept_id" jdbcType="VARCHAR"/>
            <result property="errorType" column="error_type" jdbcType="INTEGER"/>
            <result property="errorDesc" column="error_desc" jdbcType="VARCHAR"/>
            <result property="errorJson" column="error_json" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,parent_dept_id,
        dept_id,error_type,error_desc,
        error_json,create_time,update_time
    </sql>
</mapper>
