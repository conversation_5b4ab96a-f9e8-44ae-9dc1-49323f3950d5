<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wshoto.jiali.mapper.UsDepartmentMapper">

    <resultMap id="BaseResultMap" type="com.wshoto.jiali.domain.UsDepartment">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="sourceDeptId" column="source_dept_id" jdbcType="VARCHAR"/>
            <result property="sourceDeptParentId" column="source_dept_parent_id" jdbcType="VARCHAR"/>
            <result property="targetDeptId" column="target_dept_id" jdbcType="VARCHAR"/>
            <result property="targetDeptParentId" column="target_dept_parent_id" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="nameEn" column="name_en" jdbcType="VARCHAR"/>
            <result property="order" column="order" jdbcType="INTEGER"/>
            <result property="dataStatus" column="data_status" jdbcType="INTEGER"/>
            <result property="syncStatus" column="sync_status" jdbcType="INTEGER"/>
            <result property="version" column="version" jdbcType="INTEGER"/>
            <result property="deleted" column="deleted" jdbcType="SMALLINT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,source_dept_id,
        source_dept_parent_id,target_dept_id,target_dept_parent_id,
        name,name_en,"order",
        data_status,sync_status,version,
        deleted,gmt_create,gmt_modified
    </sql>

    <update id="batchUpdateDepartment" parameterType="java.util.List">
        <foreach collection="list" item="department" separator=";">
            UPDATE us_department
            SET
            id = #{department.id},
            tenant_id = #{department.tenantId},
            source_dept_id = #{department.sourceDeptId},
            source_dept_parent_id = #{department.sourceDeptParentId},
            target_dept_id = #{department.targetDeptId},
            target_dept_parent_id = #{department.targetDeptParentId},
            name = #{department.name},
            name_en = #{department.nameEn},
            "order" = #{department.order},
            data_status = #{department.dataStatus},
            sync_status = #{department.syncStatus},
            version = #{department.version},
            deleted = #{department.deleted},
            gmt_create = #{department.gmtCreate},
            gmt_modified = #{department.gmtModified}
            WHERE id = #{department.id}
        </foreach>
    </update>
    
    <select id="queryBySourceDeptId" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List" />
        from us_department where source_dept_id = #{deptId,jdbcType=VARCHAR}
    </select>

    <select id="queryByTargetDeptId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from us_department where target_dept_id = #{deptId,jdbcType=VARCHAR}
    </select>

</mapper>
