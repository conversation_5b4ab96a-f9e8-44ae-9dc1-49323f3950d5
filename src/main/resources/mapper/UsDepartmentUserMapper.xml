<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wshoto.jiali.mapper.UsDepartmentUserMapper">

    <resultMap id="BaseResultMap" type="com.wshoto.jiali.domain.UsDepartmentUser">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="sourceDeptId" column="source_dept_id" jdbcType="VARCHAR"/>
            <result property="targetDeptId" column="target_dept_id" jdbcType="VARCHAR"/>
            <result property="tartgetUserid" column="tartget_userid" jdbcType="VARCHAR"/>
            <result property="isDeptLeader" column="is_dept_leader" jdbcType="SMALLINT"/>
            <result property="deptOrder" column="dept_order" jdbcType="INTEGER"/>
            <result property="version" column="version" jdbcType="INTEGER"/>
            <result property="deleted" column="deleted" jdbcType="SMALLINT"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,source_dept_id,
        target_dept_id,tartget_userid,is_dept_leader,
        dept_order,version,deleted,
        gmt_create,gmt_modified
    </sql>


</mapper>
