DROP TABLE IF EXISTS "wechat_orgchart";
CREATE TABLE "wechat_orgchart" (
    "l1_group_code" varchar(100),
    "l1_group_en" varchar(100),
    "l1_group_cn" varchar(100),
    "l2_division_code" varchar(100),
    "l2_division_en" varchar(100),
    "l2_division_cn" varchar(100),
    "l3_business_group_code" varchar(100),
    "l3_business_group_en" varchar(100),
    "l3_business_group_cn" varchar(100),
    "l4_business_unit_code" varchar(100),
    "l4_business_unit_en" varchar(100),
    "l4_business_unit_cn" varchar(100),
    "l5_project_code" varchar(100),
    "l5_project_en" varchar(100),
    "l5_project_cn" varchar(100),
    "l6_legal_entity_code" varchar(100),
    "l6_legal_entity_en" varchar(128),
    "l6_legal_entity_cn" varchar(100),
    "l7_function_code" varchar(100),
    "l7_function_en" varchar(100),
    "l7_function_cn" varchar(100),
    "l8_department_code" varchar(100),
    "l8_department_en" varchar(100),
    "l8_department_cn" varchar(100)
)
;


DROP TABLE IF EXISTS "wechat_active_staffinfo";
CREATE TABLE "wechat_active_staffinfo" (
    "staff_english_last_name" varchar(100),
    "staff_english_first_name" varchar(100),
    "staff_chinese_last_name" varchar(100),
    "staff_chinese_first_name" varchar(100),
    "preferred_name" varchar(100),
    "formal_name_personal_info" varchar(100),
    "staff_no" varchar(100),
    "english_title" varchar(100),
    "chinese_title" varchar(100),
    "mobile_area_code" varchar(100),
    "mobile_no" varchar(100),
    "email_address_kpl" varchar(100),
    "email_address_personal" varchar(100),
    "country_region" varchar(100),
    "group_code" varchar(100),
    "division_code" varchar(100),
    "business_group_code" varchar(100),
    "business_unit_code" varchar(100),
    "project_code" varchar(100),
    "legal_entity_code" varchar(100),
    "function_code" varchar(100),
    "department" varchar(100),
    "phone_lastmodifieddatetime" date,
    "email_lastmodifieddatetime" date,
    "job_lastmodifieddatetime" date,
    "personinfo_lastmodifiedatetime" date,
    "hire_date" date,
    "system_sid" varchar(100),
    "business_phone" varchar(100),
    "staff_english_name" varchar(100),
    "staff_chinese_name" varchar(100),
    "ad_username" varchar(100),
    "gender" varchar(100),
    "mobile_countrycode" varchar(100),
    "supervisor_staffno" varchar(100),
    "supervisor_name" varchar(100),
    "supervisor_ad_username" varchar(100),
    "aad_id" varchar(50) NOT NULL DEFAULT ''::character varying
)
;


DROP TABLE IF EXISTS "wechat_inactive_staffinfo";
CREATE TABLE "wechat_inactive_staffinfo" (
    "staff_no" varchar(100),
    "system_sid" varchar(100),
    "ad_username" varchar(100)
)
;

DROP TABLE IF EXISTS "wechat_inactive_exclude_staffinfo";
CREATE TABLE "wechat_inactive_exclude_staffinfo" (
    "ad_username" varchar(100)
)
;

--------- 数据初始化 -----------
INSERT INTO "wechat_orgchart" ("l1_group_code", "l1_group_en", "l1_group_cn", "l2_division_code", "l2_division_en", "l2_division_cn", "l3_business_group_code", "l3_business_group_en", "l3_business_group_cn", "l4_business_unit_code", "l4_business_unit_en", "l4_business_unit_cn", "l5_project_code", "l5_project_en", "l5_project_cn", "l6_legal_entity_code", "l6_legal_entity_en", "l6_legal_entity_cn", "l7_function_code", "l7_function_en", "l7_function_cn", "l8_department_code", "l8_department_en", "l8_department_cn") VALUES ('KP', 'Kerry Properties', '嘉里建设', 'KPCND', 'China', '中国大陆', 'KPBG-CNPM', 'China Properties Management', 'CN物业', 'KPBU-ECNPM', 'Properties Management-East China', 'CN物业-东区', 'KPPJ-NJJLAC-PM', 'Nanjing Jinling Arcadia Court Properties Management', '南京金陵雅颂居', 'KPLE-NJKPDMSL', 'Kerry Properties Development Management (Shanghai) Co., Ltd. Nanjing Branch', '嘉里建设管理（上海）有限公司南京分公司', 'KPFN-PM-NJKPDMSL', 'Property Management', '物业管理', 'KPDP-PM-PMNJKPDMSL', 'Property Management', '物业管理部');
INSERT INTO "wechat_orgchart" ("l1_group_code", "l1_group_en", "l1_group_cn", "l2_division_code", "l2_division_en", "l2_division_cn", "l3_business_group_code", "l3_business_group_en", "l3_business_group_cn", "l4_business_unit_code", "l4_business_unit_en", "l4_business_unit_cn", "l5_project_code", "l5_project_en", "l5_project_cn", "l6_legal_entity_code", "l6_legal_entity_en", "l6_legal_entity_cn", "l7_function_code", "l7_function_en", "l7_function_cn", "l8_department_code", "l8_department_en", "l8_department_cn") VALUES ('KP', 'Kerry Properties', '嘉里建设', 'KPCND', 'China', '中国大陆', 'KPBG-CNPM', 'China Properties Management', 'CN物业', 'KPBU-NCNPM', 'Properties Management-North China', 'CN物业-北区', 'KPPJ-QHDHBT-PM', 'Qinhuangdao Habitat Properties Management', '秦皇岛海碧台', 'KPLE-QHDKPDMSL', 'Zhuoyao Property Management (Qinhuangdao) Co., Ltd., Haigang District Branch', '卓耀物业管理（秦皇岛）有限公司海港区分公司', 'KPFN-PM-QHDKPDMSL', 'Property Management', '物业管理', 'KPDP-PM-PMQHDKPDMSL', 'Property Management', '物业管理部');

INSERT INTO "wechat_active_staffinfo" ("staff_english_last_name", "staff_english_first_name", "staff_chinese_last_name", "staff_chinese_first_name", "preferred_name", "formal_name_personal_info", "staff_no", "english_title", "chinese_title", "mobile_area_code", "mobile_no", "email_address_kpl", "email_address_personal", "country_region", "group_code", "division_code", "business_group_code", "business_unit_code", "project_code", "legal_entity_code", "function_code", "department", "phone_lastmodifieddatetime", "email_lastmodifieddatetime", "job_lastmodifieddatetime", "personinfo_lastmodifiedatetime", "hire_date", "system_sid", "business_phone", "staff_english_name", "staff_chinese_name", "ad_username", "gender", "mobile_countrycode", "supervisor_staffno", "supervisor_name", "supervisor_ad_username", "aad_id") VALUES ('Zhang20240702002', 'Zhang20240702002', NULL, NULL, NULL, NULL, '', 'Senior Manager, IT Infrastructure (1001210)', NULL, '', '', '<EMAIL>', NULL, 'CHN', 'KP', 'KPCND', 'KPBG-CNCCF', 'KPBU-CNCCF', 'KPPJ-KPCPMSHNM-CCF', 'KPLE-SHKPCPM-NonMUD', 'KPFN-TI-SHKPCPMNM', 'KPDP-TI-TISHKPCPMNM', '2024-07-02', '2024-07-02', '2024-07-02', '2024-07-02', '2024-07-02', NULL, '(852) ************,(852) ************', 'ZZZ Zhang20240702002', '张（测试）', 'ZZZ <EMAIL>', NULL, '', 'K005692', 'Pei Lin, Perrin Liu', '<EMAIL>', '09f84a99-6b79-4b0d-a68d-3bc33616666777');
INSERT INTO "wechat_active_staffinfo" ("staff_english_last_name", "staff_english_first_name", "staff_chinese_last_name", "staff_chinese_first_name", "preferred_name", "formal_name_personal_info", "staff_no", "english_title", "chinese_title", "mobile_area_code", "mobile_no", "email_address_kpl", "email_address_personal", "country_region", "group_code", "division_code", "business_group_code", "business_unit_code", "project_code", "legal_entity_code", "function_code", "department", "phone_lastmodifieddatetime", "email_lastmodifieddatetime", "job_lastmodifieddatetime", "personinfo_lastmodifiedatetime", "hire_date", "system_sid", "business_phone", "staff_english_name", "staff_chinese_name", "ad_username", "gender", "mobile_countrycode", "supervisor_staffno", "supervisor_name", "supervisor_ad_username", "aad_id") VALUES ('Cheung555000', 'Kelvin555000', NULL, NULL, NULL, NULL, '', 'Senior Manager, IT Infrastructure (1001210)', NULL, '', '', '<EMAIL>', NULL, 'CHN', 'KP', 'KPCND', 'KPBG-CNCCF', 'KPBU-CNCCF', 'KPPJ-KPCPMSHNM-CCF', 'KPLE-SHKPCPM-NonMUD', 'KPFN-TI-SHKPCPMNM', 'KPDP-TI-TISHKPCPMNM', '2024-06-27', '2024-06-27', '2024-06-27', '2024-06-27', '2024-06-27', NULL, '(852) ************', 'Kelvin555000 KKL Cheung', '', '<EMAIL>', NULL, '', '3014270', 'Ya He, Grant Guo', '<EMAIL>', '09f84a99-6b79-4b0d-a68d-3bc33615556666');
INSERT INTO "wechat_active_staffinfo" ("staff_english_last_name", "staff_english_first_name", "staff_chinese_last_name", "staff_chinese_first_name", "preferred_name", "formal_name_personal_info", "staff_no", "english_title", "chinese_title", "mobile_area_code", "mobile_no", "email_address_kpl", "email_address_personal", "country_region", "group_code", "division_code", "business_group_code", "business_unit_code", "project_code", "legal_entity_code", "function_code", "department", "phone_lastmodifieddatetime", "email_lastmodifieddatetime", "job_lastmodifieddatetime", "personinfo_lastmodifiedatetime", "hire_date", "system_sid", "business_phone", "staff_english_name", "staff_chinese_name", "ad_username", "gender", "mobile_countrycode", "supervisor_staffno", "supervisor_name", "supervisor_ad_username", "aad_id") VALUES ('He2', 'Feng Shu', NULL, NULL, NULL, NULL, 'K001877', 'Security Guard (2003612)', NULL, '853', '60518233', '<EMAIL>', NULL, 'CHN', 'KP', 'KPHKD', 'KPBG-HKPJ', 'KPBU-HKPJ', 'KPPJ-HKPJ', 'KPLE-RMGL', 'KPFN-RMIR', 'KPDP-RMIR-OO', '2024-06-21', '2024-06-19', '2024-06-19', '2024-06-21', '2024-06-19', NULL, '(86) 86-755-82185555,(86) 86-755-82187777', 'FengShu4 He', '李瑞龘', '<EMAIL>', NULL, '853', '3014225', 'Jun Jun, Shirley Wu', '<EMAIL>', '7de8887f-6ab9-4ace-83ab-fd5f8546e780');
INSERT INTO "wechat_active_staffinfo" ("staff_english_last_name", "staff_english_first_name", "staff_chinese_last_name", "staff_chinese_first_name", "preferred_name", "formal_name_personal_info", "staff_no", "english_title", "chinese_title", "mobile_area_code", "mobile_no", "email_address_kpl", "email_address_personal", "country_region", "group_code", "division_code", "business_group_code", "business_unit_code", "project_code", "legal_entity_code", "function_code", "department", "phone_lastmodifieddatetime", "email_lastmodifieddatetime", "job_lastmodifieddatetime", "personinfo_lastmodifiedatetime", "hire_date", "system_sid", "business_phone", "staff_english_name", "staff_chinese_name", "ad_username", "gender", "mobile_countrycode", "supervisor_staffno", "supervisor_name", "supervisor_ad_username", "aad_id") VALUES ('Cheung', 'Kelvin555', NULL, NULL, NULL, NULL, '', 'Senior Manager, IT Infrastructure (1001210)', NULL, '', '', '<EMAIL>', NULL, 'CHN', 'KP', 'KPHKD', 'KPBG-HKPJ', 'KPBU-HKPJ', 'KPPJ-HKPJ', 'KPLE-RMGL', 'KPFN-RMIR', 'KPDP-RMIR-OO', '2024-06-21', '2024-06-21', '2024-06-21', '2024-06-21', '2024-06-19', NULL, '(852) 29676666,(852) 29677777', 'Kelvin555 KKL Cheung', '陈凯文（测试2）', '<EMAIL>', NULL, '', '3014225', 'Jun Jun, Shirley Wu', '<EMAIL>', '09f84a99-6b79-4b0d-a68d-3bc3361964d8');
INSERT INTO "wechat_active_staffinfo" ("staff_english_last_name", "staff_english_first_name", "staff_chinese_last_name", "staff_chinese_first_name", "preferred_name", "formal_name_personal_info", "staff_no", "english_title", "chinese_title", "mobile_area_code", "mobile_no", "email_address_kpl", "email_address_personal", "country_region", "group_code", "division_code", "business_group_code", "business_unit_code", "project_code", "legal_entity_code", "function_code", "department", "phone_lastmodifieddatetime", "email_lastmodifieddatetime", "job_lastmodifieddatetime", "personinfo_lastmodifiedatetime", "hire_date", "system_sid", "business_phone", "staff_english_name", "staff_chinese_name", "ad_username", "gender", "mobile_countrycode", "supervisor_staffno", "supervisor_name", "supervisor_ad_username", "aad_id") VALUES ('Fu', 'Eleven', NULL, NULL, NULL, NULL, '-', 'TI Contractor', NULL, '86', '***********', '<EMAIL>', NULL, 'CHN', 'KP', 'KPCND', 'KPBG-CNPM', 'KPBU-ECNPM', 'KPPJ-NJJLAC-PM', 'KPLE-NJKPDMSL', 'KPFN-PM-NJKPDMSL', 'KPDP-PM-PMNJKPDMSL', '2025-01-14', '2025-01-14', '2025-01-14', '2025-01-14', '2025-01-14', NULL, '***********', 'Eleven Fu', '付海', '<EMAIL>', 'M', '86', 'K005692', 'Pei Lin, Perrin Liu', '<EMAIL>', '4f97202e-a895-4854-b4e1-5f5729bb9c17');
INSERT INTO "wechat_active_staffinfo" ("staff_english_last_name", "staff_english_first_name", "staff_chinese_last_name", "staff_chinese_first_name", "preferred_name", "formal_name_personal_info", "staff_no", "english_title", "chinese_title", "mobile_area_code", "mobile_no", "email_address_kpl", "email_address_personal", "country_region", "group_code", "division_code", "business_group_code", "business_unit_code", "project_code", "legal_entity_code", "function_code", "department", "phone_lastmodifieddatetime", "email_lastmodifieddatetime", "job_lastmodifieddatetime", "personinfo_lastmodifiedatetime", "hire_date", "system_sid", "business_phone", "staff_english_name", "staff_chinese_name", "ad_username", "gender", "mobile_countrycode", "supervisor_staffno", "supervisor_name", "supervisor_ad_username", "aad_id") VALUES ('Gu', 'Alan', NULL, NULL, NULL, NULL, '-', 'TI Contractor', NULL, '86', '***********', '<EMAIL>', NULL, 'CHN', 'KP', 'KPCND', 'KPBG-CNPM', 'KPBU-NCNPM', 'KPPJ-QHDHBT-PM', 'KPLE-QHDKPDMSL', 'KPFN-PM-QHDKPDMSL', 'KPDP-PM-PMQHDKPDMSL', '2025-01-14', '2025-01-14', '2025-01-14', '2025-01-14', '2025-01-14', NULL, '***********', 'Alan Gu', '顾晨辉', '<EMAIL>', 'M', '86', '3014270', 'Ya He, Grant Guo', '<EMAIL>', '72f86962-4f3e-4fd5-9ccc-bfd1946ee036');
INSERT INTO "wechat_active_staffinfo" ("staff_english_last_name", "staff_english_first_name", "staff_chinese_last_name", "staff_chinese_first_name", "preferred_name", "formal_name_personal_info", "staff_no", "english_title", "chinese_title", "mobile_area_code", "mobile_no", "email_address_kpl", "email_address_personal", "country_region", "group_code", "division_code", "business_group_code", "business_unit_code", "project_code", "legal_entity_code", "function_code", "department", "phone_lastmodifieddatetime", "email_lastmodifieddatetime", "job_lastmodifieddatetime", "personinfo_lastmodifiedatetime", "hire_date", "system_sid", "business_phone", "staff_english_name", "staff_chinese_name", "ad_username", "gender", "mobile_countrycode", "supervisor_staffno", "supervisor_name", "supervisor_ad_username", "aad_id") VALUES ('Chen', 'Anson', NULL, NULL, NULL, NULL, '', 'TI Contractor', NULL, '86', '***********', '<EMAIL>', NULL, 'CHN', 'KP', 'KPCND', 'KPBG-CNPM', 'KPBU-SCNPM', 'KPPJ-SZAC-PM', 'KPLE-SZKPDMSLAC', 'KPFN-PM-SZKPDMSLAC', 'KPDP-PM-PMSZKPDMSLAC', NULL, '2025-01-14', '2025-01-14', '2025-01-14', '2025-01-14', NULL, '', 'Anson Chen', '陈孟超', '<EMAIL>', 'M', '86', '3014225', 'Jun Jun, Shirley Wu', '<EMAIL>', 'ad3cf36d-ba08-469b-baa4-eb3e747e6023');
INSERT INTO "wechat_active_staffinfo" ("staff_english_last_name", "staff_english_first_name", "staff_chinese_last_name", "staff_chinese_first_name", "preferred_name", "formal_name_personal_info", "staff_no", "english_title", "chinese_title", "mobile_area_code", "mobile_no", "email_address_kpl", "email_address_personal", "country_region", "group_code", "division_code", "business_group_code", "business_unit_code", "project_code", "legal_entity_code", "function_code", "department", "phone_lastmodifieddatetime", "email_lastmodifieddatetime", "job_lastmodifieddatetime", "personinfo_lastmodifiedatetime", "hire_date", "system_sid", "business_phone", "staff_english_name", "staff_chinese_name", "ad_username", "gender", "mobile_countrycode", "supervisor_staffno", "supervisor_name", "supervisor_ad_username", "aad_id") VALUES ('Miao', 'FengYan', NULL, NULL, NULL, NULL, '', 'Contractor', NULL, '', '', '<EMAIL>', NULL, 'CHN', 'KP', 'KPCND', 'KPBG-CNPJ', 'KPBU-MUDPJ', 'KPPJ-KON-PJ', 'KPLE-SHKCTS', 'KPFN-AM-SHKCTS', 'KPDP-MKT-AMSHKCTS', NULL, '2025-01-14', '2025-01-14', '2025-01-14', '2025-01-14', NULL, '', 'FengYan Miao', '缪凤妍', '<EMAIL>', 'F', '', 'K006241', 'Jun, Jessica Lu', '<EMAIL>', '37ce6817-82a6-458b-9709-c338c73921cd');

INSERT INTO "wechat_inactive_staffinfo" ("staff_no", "system_sid", "ad_username") VALUES ('3013074', '3013074', '<EMAIL>');
INSERT INTO "wechat_inactive_staffinfo" ("staff_no", "system_sid", "ad_username") VALUES ('', '', 'ZZZ <EMAIL>');
INSERT INTO "wechat_inactive_staffinfo" ("staff_no", "system_sid", "ad_username") VALUES ('', '', '<EMAIL>');

INSERT INTO "wechat_inactive_exclude_staffinfo" ("ad_username") VALUES ('<EMAIL>');


--------- tick4.5 员工信息来源替换为tb_wechat_stuff ---------
DROP TABLE IF EXISTS "tb_wechat_stuff";
CREATE TABLE "tb_wechat_stuff" (
    "staff_no" varchar(16)  NOT NULL ,
    "english_title" varchar(100)  NOT NULL ,
    "chinese_title" varchar(100)  NOT NULL ,
    "mobile_phone" varchar(100)  NOT NULL ,
    "email_address_kpl" varchar(128)  NOT NULL ,
    "department" varchar(50)  NOT NULL,
    "create_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "business_phone" varchar(100)  NOT NULL ,
    "staff_english_name" varchar(100)  NOT NULL ,
    "staff_chinese_name" varchar(100)  NOT NULL ,
    "ad_username" varchar(128)  NOT NULL ,
    "gender" varchar(1)  NOT NULL ,
    "supervisor_staffno" varchar(100)  NOT NULL ,
    "supervisor_aad_id" varchar(50)  NOT NULL ,
    "supervisor_ad_username" varchar(128)  NOT NULL ,
    "deleted" int2 NOT NULL DEFAULT 0,
    "aad_id" varchar(50)  NOT NULL
)
;

ALTER TABLE "tb_wechat_stuff" ADD CONSTRAINT "pk_tb_wechat_stuff" PRIMARY KEY ("aad_id");


INSERT INTO "tb_wechat_stuff" ("staff_no", "english_title", "chinese_title", "mobile_phone", "email_address_kpl", "department", "create_time", "update_time", "business_phone", "staff_english_name", "staff_chinese_name", "ad_username", "gender", "supervisor_staffno", "supervisor_aad_id", "supervisor_ad_username", "deleted", "aad_id") VALUES ('', 'Senior Manager, IT Infrastructure (1001210)', '高级经理-机电', '', 'ZZZ <EMAIL>', 'KPDP-TI-TISHKPCPMNM', '2025-04-28 18:53:47.687013', '2025-04-28 18:53:47.687013', '(852) ************,(852) ************', 'ZZZ Zhang20240702002', '张（测试）', '<EMAIL>', '', 'K005692', 'c1efd41f-1367-4741-83ff-d430c43fbf89', 'Pei Lin, Perrin Liu', 0, 'ff891011-57c7-44d8-b9fb-6af47e4567gj');
INSERT INTO "tb_wechat_stuff" ("staff_no", "english_title", "chinese_title", "mobile_phone", "email_address_kpl", "department", "create_time", "update_time", "business_phone", "staff_english_name", "staff_chinese_name", "ad_username", "gender", "supervisor_staffno", "supervisor_aad_id", "supervisor_ad_username", "deleted", "aad_id") VALUES ('3014980', 'Senior Manager, IT Infrastructure (1001210)', '高级经理-建设', '', '<EMAIL>', 'KPDP-TI-TISHKPCPMNM', '2025-04-28 18:53:47.687013', '2025-04-28 18:53:47.687013', '(852) ************', 'ZKelvin555000 KKL Cheung', '陈凯（测试）', '<EMAIL>', '', '3014270', 'c1efd41f-1367-4741-83ff-d430c4dpk876', 'Ya He, Grant Guo', 0, 'ff891011-57c7-44d8-b9fb-6af47cdac1ec');
INSERT INTO "tb_wechat_stuff" ("staff_no", "english_title", "chinese_title", "mobile_phone", "email_address_kpl", "department", "create_time", "update_time", "business_phone", "staff_english_name", "staff_chinese_name", "ad_username", "gender", "supervisor_staffno", "supervisor_aad_id", "supervisor_ad_username", "deleted", "aad_id") VALUES ('60518233', 'Security Guard (2003612)', '高级安保', '', '<EMAIL>', 'KPDP-RMIR-OO', '2025-04-28 18:53:47.687013', '2025-04-28 18:53:47.687013', '(86) 86-755-82185555,(86) 86-755-82187777', 'FengShu.He', '李瑞龘', '<EMAIL>', '', '3014225', 'c1efd41f-1367-4741-83ff-de24gbdpk876', '<EMAIL>', 0, '7de8887f-6ab9-4ace-83ab-fd5f8546e780');
INSERT INTO "tb_wechat_stuff" ("staff_no", "english_title", "chinese_title", "mobile_phone", "email_address_kpl", "department", "create_time", "update_time", "business_phone", "staff_english_name", "staff_chinese_name", "ad_username", "gender", "supervisor_staffno", "supervisor_aad_id", "supervisor_ad_username", "deleted", "aad_id") VALUES ('3014225', 'Senior Manager, IT Infrastructure (1001210)', '高级经理-IT', '', '<EMAIL>', 'KPDP-RMIR-OO', '2025-04-28 18:53:47.687013', '2025-04-28 18:53:47.687013', '(852) 29676666,(852) 2967777', 'Kelvin555 KKL Cheung', '陈凯文（测试2）', '<EMAIL>', '', '3014225', 'c1efd41f-1367-4741-83ff-de24gbnbv765', '<EMAIL>', 0, '09f84a99-6b79-4b0d-a68d-3bc3361964d8');