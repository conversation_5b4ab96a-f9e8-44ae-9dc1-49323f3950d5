DROP TABLE IF EXISTS "us_user";
CREATE TABLE "us_user" (
    "id" int8 NOT NULL,
    "source_userid" varchar(64)  NOT NULL,
    "name" varchar(64),
    "alias" varchar(64),
    "mobile" varchar(20),
    "position" varchar(128),
    "gender" varchar(20),
    "biz_mail" varchar(64),
    "telephone" varchar(32),
    "direct_leader" varchar(64),
    "avatar_mediaid" varchar(50),
    "enable" int2,
    "extattr" varchar,
    "to_invite" int2 DEFAULT 1,
    "external_position" varchar(12),
    "external_profile" varchar,
    "address" varchar(128),
    "main_department" int4,
    "data_status" int4,
    "sync_status" int4 DEFAULT 0,
    "version" int4 NOT NULL DEFAULT 0,
    "deleted" int2 NOT NULL DEFAULT 0,
    "gmt_create" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "gmt_modified" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    "target_userid" varchar(64) NOT NULL,
    "aad_id" varchar(50) NOT NULL,
    "tenant_id" varchar(50) ,
    "email" varchar(64)
)
;
ALTER TABLE "us_user" ADD CONSTRAINT "us_user_pk" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "us_department";
CREATE TABLE "us_department" (
    "id" int8 NOT NULL,
    "source_dept_id" varchar(200),
    "source_dept_parent_id" varchar(200),
    "target_dept_id" varchar(50),
    "target_dept_parent_id" varchar(50),
    "name" varchar(100) NOT NULL,
    "name_en" varchar(100),
    "order" int4 DEFAULT 0,
    "data_status" int4,
    "sync_status" int4 DEFAULT 0,
    "version" int4 NOT NULL DEFAULT 0,
    "deleted" int2 NOT NULL DEFAULT 0,
    "gmt_create" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "gmt_modified" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    "tenant_id" varchar(50)
)
;
ALTER TABLE "us_department" ADD CONSTRAINT "us_department_pk" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "us_department_user";
CREATE TABLE "us_department_user" (
    "id" int8 NOT NULL,
    "source_dept_id" varchar(100),
    "target_dept_id" varchar(100),
    "tartget_userid" varchar(64),
    "aad_id" varchar(50) NOT NULL,
    "is_dept_leader" int2,
    "dept_order" int4,
    "version" int4 NOT NULL DEFAULT 0,
    "deleted" int2 NOT NULL DEFAULT 0,
    "gmt_create" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "gmt_modified" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    "tenant_id" varchar(50)
)
;
ALTER TABLE "us_department_user" ADD CONSTRAINT "us_department_user_pk" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "us_notify_config";
CREATE TABLE "us_notify_config" (
    "id" int8 NOT NULL,
    "notify_type" int4,
    "is_open" int2,
    "mail" varchar(255),
    "username" varchar(255),
    "password" varchar(255),
    "host" varchar(255),
    "port" int8 NOT NULL,
    "robot_key" varchar(200),
    "version" int4 NOT NULL DEFAULT 0,
    "deleted" int2 NOT NULL DEFAULT 0,
    "gmt_create" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "gmt_modified" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    "to_user" varchar,
    "tenant_id" varchar(50),
    "ssl_enable" bool NOT NULL DEFAULT false,
    "time_out" int4
)
;
ALTER TABLE "us_notify_config" ADD CONSTRAINT "us_notify_config_pk" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "us_logged_user";
CREATE TABLE "us_logged_user" (
    "aad_id" varchar(50) NOT NULL DEFAULT,
    "source_userid" varchar(128) NOT NULL DEFAULT,
    "create_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP
)
;

ALTER TABLE "us_logged_user" ADD CONSTRAINT "pk_us_logged_user" PRIMARY KEY ("aad_id");