spring:
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        db_qa_wecom_datasync:   # 数据库1
          driver-class-name: org.h2.Driver
          url: jdbc:h2:mem:db_qa_wecom_datasync_db;DB_CLOSE_DELAY=-1;MODE=PostgreSQL;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
          username: test
          password:
          init:
            schema: classpath:db/db_qa_wecom_datasync_initial_db.sql
        db_qa_wecom:   # 数据库2
          driver-class-name: org.h2.Driver
          url: jdbc:h2:mem:db_qa_wecom_db;MODE=PostgreSQL;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
          username: test
          password:
          init:
            schema: classpath:db/db_qa_wecom_initial_db.sql
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER

mybatis-plus:
  configuration:
    # 开启sql日志
    #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    #关闭sql日志
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl


logging:
  config: classpath:log4j2.xml

email:
  send-to-list: <EMAIL>,<EMAIL>
  services:
    unified-messaging: https://unified-messaging-service

jiali:
  tenantid: ww6ffc4f642bca6ae8
  mail: <EMAIL>
  username: <EMAIL>
  password: K1rry+Platform#2022!
  host: smtp.mailrelay.cn
  port: 465
  whiteDeptL2: KPCND
  whiteDept: KPDP-RMIR-OO,KPPJ-HKPpppp,KPDP-TI-TISHKPCPMNM,KPDP-KPFIN-COD,KPDP-NOT-EXIST
  externalPosition: 员工
  extattrName: 职务(中)
  externalCorpName: 嘉里（中国）项目管理上海分公司
