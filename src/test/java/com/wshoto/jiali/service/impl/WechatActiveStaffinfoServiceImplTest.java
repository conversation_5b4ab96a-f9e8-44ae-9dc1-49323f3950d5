package com.wshoto.jiali.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.wshoto.jiali.config.JiaLiConfig;
import com.wshoto.jiali.domain.Attrs;
import com.wshoto.jiali.domain.EmailSendCommandVo;
import com.wshoto.jiali.domain.Extattr;
import com.wshoto.jiali.domain.Text;
import com.wshoto.jiali.domain.UsDepartment;
import com.wshoto.jiali.domain.UsDepartmentUser;
import com.wshoto.jiali.domain.UsUser;
import com.wshoto.jiali.domain.WechatInactiveExcludeStaffinfo;
import com.wshoto.jiali.domain.WechatOrgchart;
import com.wshoto.jiali.domain.WechatStuffDTO;
import com.wshoto.jiali.enums.DataStatusEnum;
import com.wshoto.jiali.enums.DeleteStatusEnum;
import com.wshoto.jiali.enums.SyncStatusEnum;
import com.wshoto.jiali.feign.clients.MessageClient;
import com.wshoto.jiali.mapper.UsUserMapper;
import com.wshoto.jiali.service.JialiUsSyncLogService;
import com.wshoto.jiali.service.UsDepartmentService;
import com.wshoto.jiali.service.UsDepartmentUserService;
import com.wshoto.jiali.service.UsUserService;
import com.wshoto.jiali.service.WechatInactiveExcludeStaffinfoService;
import com.wshoto.jiali.service.WechatOrgchartService;
import com.wshoto.jiali.service.WechatStaffDTOService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;
import org.springframework.test.util.ReflectionTestUtils;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.IContext;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.hutool.core.util.RandomUtil.randomString;
import static com.wshoto.jiali.enums.DeleteStatusEnum.NOT_DELETED;
import static com.wshoto.jiali.enums.UserEnableEnum.ENABLE;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("微盛-同步员工信息-单元测试")
class WechatActiveStaffinfoServiceImplTest {

    @Mock
    private Environment environment;

    @Mock
    private JiaLiConfig jiaLiConfig;

    @Mock
    private MessageClient messageClient;

    @Mock
    private UsUserMapper usUserMapper;

    @Mock
    private JialiUsSyncLogService jialiUsSyncLogService;

    @Mock
    private WechatOrgchartService wechatOrgchartService;

    @Mock
    private WechatStaffDTOService wechatStaffDTOService;

    @Mock
    private UsDepartmentService usDepartmentService;

    @Mock
    private UsUserService usUserService;

    @Mock
    private UsDepartmentUserService usDepartmentUserService;

    @Mock
    private WechatInactiveExcludeStaffinfoService wechatInactiveExcludeStaffinfoService;

    @Mock
    private TemplateEngine templateEngine;

    @InjectMocks
    private WechatActiveStaffinfoServiceImpl wechatActiveStaffinfoServiceImpl;

    @Test
    @DisplayName("微盛-同步员工信息-正常场景：删除赦免表为空")
    void _01_getSfInactiveExcludeStaffAdUsernames_data_empty_success() {
        // Arrange
        doReturn(Collections.emptyList()).when(wechatInactiveExcludeStaffinfoService)
                                         .list();

        // Act
        List<String> sfInactiveExcludeStaffAdUsernames =
                wechatActiveStaffinfoServiceImpl.getSfInactiveExcludeStaffAdUsernames();

        // Assert
        assertTrue(CollectionUtils.isEmpty(sfInactiveExcludeStaffAdUsernames));
    }

    @Test
    @DisplayName("微盛-同步员工信息-正常场景：删除赦免表当中adUsername字段为空")
    void _02_getSfInactiveExcludeStaffAdUsernames_adUsername_empty_success() {
        // Arrange
        var wechatInactiveExcludeStaffinfoTest1 = new WechatInactiveExcludeStaffinfo();
        wechatInactiveExcludeStaffinfoTest1.setAdUsername(null);

        var wechatInactiveExcludeStaffinfoTest2 = new WechatInactiveExcludeStaffinfo();
        wechatInactiveExcludeStaffinfoTest2.setAdUsername("");
        doReturn(List.of(wechatInactiveExcludeStaffinfoTest1, wechatInactiveExcludeStaffinfoTest2)).when(
                                                                                                           wechatInactiveExcludeStaffinfoService)
                                                                                                   .list();

        // Act
        List<String> sfInactiveExcludeStaffAdUsernames =
                wechatActiveStaffinfoServiceImpl.getSfInactiveExcludeStaffAdUsernames();

        // Assert
        assertTrue(CollectionUtils.isEmpty(sfInactiveExcludeStaffAdUsernames));
    }

    @Test
    @DisplayName("微盛-同步员工信息-正常场景：数据正常")
    void _03_getSfInactiveExcludeStaffAdUsernames_success() {
        // Arrange
        var wechatInactiveExcludeStaffinfoTest1 = new WechatInactiveExcludeStaffinfo();
        wechatInactiveExcludeStaffinfoTest1.setAdUsername(Math.random() + "");

        var wechatInactiveExcludeStaffinfoTest2 = new WechatInactiveExcludeStaffinfo();
        wechatInactiveExcludeStaffinfoTest2.setAdUsername(Math.random() + "");
        doReturn(List.of(wechatInactiveExcludeStaffinfoTest1, wechatInactiveExcludeStaffinfoTest2)).when(
                                                                                                           wechatInactiveExcludeStaffinfoService)
                                                                                                   .list();

        // Act
        List<String> sfInactiveExcludeStaffAdUsernames =
                wechatActiveStaffinfoServiceImpl.getSfInactiveExcludeStaffAdUsernames();

        // Assert
        assertEquals(2, sfInactiveExcludeStaffAdUsernames.size());
    }

    @Test
    @DisplayName("微盛-同步员工信息-发送邮件：正常场景")
    void _04_sendEmail_success() {
        // Arrange
        lenient().when(templateEngine.process(anyString(), any(IContext.class)))
                 .thenReturn("模拟的邮件内容");

        // Act and assert
        assertDoesNotThrow(() -> wechatActiveStaffinfoServiceImpl.sendErrorEmail(StringUtils.EMPTY));
    }

    @Test
    @DisplayName("微盛-同步员工信息-员工启用状态设置：员工状态为启用")
    void _05_getAddUserList_enabledUser_success() {
        // Arrange
        lenient().when(templateEngine.process(anyString(), any(IContext.class)))
                 .thenReturn("模拟的邮件内容");

        WechatStuffDTO wechatStuffDTO = new WechatStuffDTO();
        wechatStuffDTO.setEnabled(1); // 设置为启用状态
        wechatStuffDTO.setAadId("test-aad-id");
        wechatStuffDTO.setAdUsername("test-username");
        wechatStuffDTO.setStaffChineseName("测试用户");
        wechatStuffDTO.setDepartment("test-department"); // 设置部门

        List<UsUser> addUsUserList = new ArrayList<>();
        List<UsDepartmentUser> addUsDepartmentUserList = new ArrayList<>();
        Map<String, UsDepartment> usDepartmentMap = new HashMap<>();

        // 创建并添加部门信息
        UsDepartment usDepartment = new UsDepartment();
        usDepartment.setSourceDeptId("test-department");
        usDepartment.setTargetDeptId("1");
        usDepartment.setDeleted(0); // 未删除
        usDepartmentMap.put("test-department", usDepartment);

        // 设置必要的配置
        JiaLiConfig jiaLiConfig = new JiaLiConfig();
        jiaLiConfig.setTenantId("test-tenant-id");
        jiaLiConfig.setExternalPosition("员工");
        ReflectionTestUtils.setField(wechatActiveStaffinfoServiceImpl, "jiaLiConfig", jiaLiConfig);

        // Act - 调用私有方法
        ReflectionTestUtils.invokeMethod(wechatActiveStaffinfoServiceImpl, "getAddUserList", addUsUserList,
                                         addUsDepartmentUserList, wechatStuffDTO, usDepartmentMap);

        // Assert
        assertEquals(1, addUsUserList.size(), "应该添加一个用户到列表中");
        UsUser addedUser = addUsUserList.get(0);
        assertEquals(1, addedUser.getEnable(), "启用状态应该为1（启用）");
        assertEquals("test-username", addedUser.getSourceUserid(), "源用户ID应该正确设置");
        assertEquals("测试用户", addedUser.getName(), "用户名称应该正确设置");
        assertEquals("test-tenant-id", addedUser.getTenantId(), "租户ID应该正确设置");
        assertEquals(1, addedUser.getMainDepartment(), "主部门ID应该正确设置");
    }

    @Test
    @DisplayName("微盛-同步员工信息-员工启用状态设置：员工状态为禁用")
    void _06_getAddUserList_disabledUser_success() {
        // Arrange
        lenient().when(templateEngine.process(anyString(), any(IContext.class)))
                 .thenReturn("模拟的邮件内容");

        WechatStuffDTO wechatStuffDTO = new WechatStuffDTO();
        wechatStuffDTO.setEnabled(0); // 设置为禁用状态
        wechatStuffDTO.setAadId("test-aad-id");
        wechatStuffDTO.setAdUsername("test-username");
        wechatStuffDTO.setStaffChineseName("测试用户");
        wechatStuffDTO.setDepartment("test-department"); // 设置部门

        List<UsUser> addUsUserList = new ArrayList<>();
        List<UsDepartmentUser> addUsDepartmentUserList = new ArrayList<>();
        Map<String, UsDepartment> usDepartmentMap = new HashMap<>();

        // 创建并添加部门信息
        UsDepartment usDepartment = new UsDepartment();
        usDepartment.setSourceDeptId("test-department");
        usDepartment.setTargetDeptId("1");
        usDepartment.setDeleted(0); // 未删除
        usDepartmentMap.put("test-department", usDepartment);

        // 设置必要的配置
        JiaLiConfig jiaLiConfig = new JiaLiConfig();
        jiaLiConfig.setTenantId("test-tenant-id");
        jiaLiConfig.setExternalPosition("员工");
        ReflectionTestUtils.setField(wechatActiveStaffinfoServiceImpl, "jiaLiConfig", jiaLiConfig);

        // Act - 调用私有方法
        ReflectionTestUtils.invokeMethod(wechatActiveStaffinfoServiceImpl, "getAddUserList", addUsUserList,
                                         addUsDepartmentUserList, wechatStuffDTO, usDepartmentMap);

        // Assert
        assertEquals(1, addUsUserList.size(), "应该添加一个用户到列表中");
        UsUser addedUser = addUsUserList.get(0);
        assertEquals(0, addedUser.getEnable(), "启用状态应该为0（禁用）");
        assertEquals("test-username", addedUser.getSourceUserid(), "源用户ID应该正确设置");
        assertEquals("测试用户", addedUser.getName(), "用户名称应该正确设置");
        assertEquals("test-tenant-id", addedUser.getTenantId(), "租户ID应该正确设置");
        assertEquals(1, addedUser.getMainDepartment(), "主部门ID应该正确设置");
    }

    @Test
    @DisplayName("微盛-同步员工信息-员工状态变更检测：状态未变更")
    void _07_getNeedUpdateUserList_statusNotChanged_success() {
        // Arrange
        // 创建模拟数据
        WechatStuffDTO wechatStuffDTO = new WechatStuffDTO();
        wechatStuffDTO.setEnabled(1); // 设置为启用状态
        wechatStuffDTO.setAadId("test-aad-id");

        // 创建数据库中已存在的用户
        UsUser dbUser = new UsUser();
        dbUser.setEnable(1); // 数据库中也是启用状态
        dbUser.setAadId("test-aad-id");

        // 创建用户映射
        Map<String, UsUser> dbUserMap = new HashMap<>();
        dbUserMap.put("test-aad-id", dbUser);

        // 创建StringBuilder用于捕获更新原因
        StringBuilder updateReasons = new StringBuilder();

        // Act - 执行状态比较逻辑
        boolean result =
                ReflectionTestUtils.invokeMethod(wechatActiveStaffinfoServiceImpl, "compareEmployeeStatus", dbUser,
                                                 wechatStuffDTO, updateReasons);

        // Assert
        assertFalse(result, "状态未变更，应该返回false");
        assertEquals("", updateReasons.toString(), "更新原因应该为空");
    }

    @Test
    @DisplayName("微盛-同步员工信息-员工状态变更检测：状态从启用变为禁用")
    void _08_getNeedUpdateUserList_statusChangedToDisabled_success() {
        // Arrange
        // 创建模拟数据
        WechatStuffDTO wechatStuffDTO = new WechatStuffDTO();
        wechatStuffDTO.setEnabled(0); // 设置为禁用状态
        wechatStuffDTO.setAadId("test-aad-id");

        // 创建数据库中已存在的用户
        UsUser dbUser = new UsUser();
        dbUser.setEnable(1); // 数据库中是启用状态
        dbUser.setAadId("test-aad-id");

        // 创建用户映射
        Map<String, UsUser> dbUserMap = new HashMap<>();
        dbUserMap.put("test-aad-id", dbUser);

        // 创建StringBuilder用于捕获更新原因
        StringBuilder updateReasons = new StringBuilder();

        // Act - 执行状态比较逻辑
        boolean result =
                ReflectionTestUtils.invokeMethod(wechatActiveStaffinfoServiceImpl, "compareEmployeeStatus", dbUser,
                                                 wechatStuffDTO, updateReasons);

        // Assert
        assertTrue(result, "状态已变更，应该返回true");
        assertEquals("员工状态变更(原状态：1，新状态：0); ", updateReasons.toString(), "更新原因应该包含状态变更信息");
    }

    @Test
    @DisplayName("微盛-同步员工信息-员工状态变更检测：状态从禁用变为启用")
    void _09_getNeedUpdateUserList_statusChangedToEnabled_success() {
        // Arrange
        // 创建模拟数据
        WechatStuffDTO wechatStuffDTO = new WechatStuffDTO();
        wechatStuffDTO.setEnabled(1); // 设置为启用状态
        wechatStuffDTO.setAadId("test-aad-id");

        // 创建数据库中已存在的用户
        UsUser dbUser = new UsUser();
        dbUser.setEnable(0); // 数据库中是禁用状态
        dbUser.setAadId("test-aad-id");

        // 创建用户映射
        Map<String, UsUser> dbUserMap = new HashMap<>();
        dbUserMap.put("test-aad-id", dbUser);

        // 创建StringBuilder用于捕获更新原因
        StringBuilder updateReasons = new StringBuilder();

        // Act - 执行状态比较逻辑
        boolean result =
                ReflectionTestUtils.invokeMethod(wechatActiveStaffinfoServiceImpl, "compareEmployeeStatus", dbUser,
                                                 wechatStuffDTO, updateReasons);

        // Assert
        assertTrue(result, "状态已变更，应该返回true");
        assertEquals("员工状态变更(原状态：0，新状态：1); ", updateReasons.toString(), "更新原因应该包含状态变更信息");
    }

    @Test
    @DisplayName("同步员工信息-正常流程：修改场景，部门修改")
    void syncStaffInfo_update_department_normalFlow() {
        // Arrange
        mockWechatOrgchart();

        List<WechatStuffDTO> wechatStuffDTOList = new ArrayList<>();
        var wechatStuffDTO = new WechatStuffDTO();
        wechatStuffDTO.setAdUsername("test-user");
        wechatStuffDTO.setAadId("test-aad-id");
        wechatStuffDTO.setDepartment("L8-Dept1");
        wechatStuffDTO.setDeleted(NOT_DELETED.getCode());
        wechatStuffDTOList.add(wechatStuffDTO);

        LambdaQueryChainWrapper<WechatStuffDTO> lambdaQueryChainWrapperWechatStuffDTO =
                mock(LambdaQueryChainWrapper.class);
        when(wechatStaffDTOService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.in(any(), anyList())).thenReturn(
                lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.isNotNull(any())).thenReturn(lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.list()).thenReturn(wechatStuffDTOList);

        List<UsDepartment> usDepartmentList = new ArrayList<>();
        UsDepartment usDepartment = new UsDepartment();
        usDepartment.setSourceDeptId("L8-Dept1");
        usDepartment.setTargetDeptId("1");
        usDepartment.setDeleted(NOT_DELETED.getCode());
        usDepartmentList.add(usDepartment);

        LambdaQueryChainWrapper<UsDepartment> lambdaQueryChainWrapperUsDepartment = mock(LambdaQueryChainWrapper.class);
        when(usDepartmentService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.in(any(), anyList())).thenReturn(lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.eq(any(), eq(NOT_DELETED.getCode()))).thenReturn(
                lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.eq(any(), anyString())).thenReturn(
                lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.list()).thenReturn(usDepartmentList);

        List<UsUser> dbUserList = new ArrayList<>();
        UsUser dbUser = new UsUser();
        dbUser.setAadId("test-aad-id");
        dbUser.setMainDepartment(1);
        dbUser.setDeleted(NOT_DELETED.getCode());
        dbUserList.add(dbUser);

        LambdaQueryChainWrapper<UsUser> lambdaQueryChainWrapperUsUser = mock(LambdaQueryChainWrapper.class);
        when(usUserService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.in(any(), anyList())).thenReturn(lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.eq(any(), eq(NOT_DELETED.getCode()))).thenReturn(
                lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.list()).thenReturn(dbUserList);

        var usDepartmentUser = new UsDepartmentUser();
        usDepartmentUser.setSourceDeptId("L8-Dept2"); // 部门修改
        when(usDepartmentUserService.list(any(LambdaQueryWrapper.class))).thenReturn(List.of(usDepartmentUser));

        // Act
        wechatActiveStaffinfoServiceImpl.syncStaffInfo();

        // Assert
        verify(wechatOrgchartService, times(1)).lambdaQuery();
        verify(wechatStaffDTOService, times(1)).lambdaQuery();
        verify(usDepartmentService, times(3)).lambdaQuery();
        verify(usUserService, times(1)).lambdaQuery();
    }

    @Test
    @DisplayName("同步员工信息-异常场景：修改场景，部门修改，部门不存在")
    void syncStaffInfo_update_departmentNotFound_errorFlow() {
        // Arrange
        mockWechatOrgchart();

        List<WechatStuffDTO> wechatStuffDTOList = new ArrayList<>();
        var wechatStuffDTO = new WechatStuffDTO();
        wechatStuffDTO.setAdUsername("test-user");
        wechatStuffDTO.setAadId("test-aad-id");
        wechatStuffDTO.setDepartment("L8-Dept1");
        wechatStuffDTO.setDeleted(NOT_DELETED.getCode());
        wechatStuffDTOList.add(wechatStuffDTO);

        LambdaQueryChainWrapper<WechatStuffDTO> lambdaQueryChainWrapperWechatStuffDTO =
                mock(LambdaQueryChainWrapper.class);
        when(wechatStaffDTOService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.in(any(), anyList())).thenReturn(
                lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.isNotNull(any())).thenReturn(lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.list()).thenReturn(wechatStuffDTOList);

        LambdaQueryChainWrapper<UsDepartment> lambdaQueryChainWrapperUsDepartment = mock(LambdaQueryChainWrapper.class);
        when(usDepartmentService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.in(any(), anyList())).thenReturn(lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.eq(any(), eq(NOT_DELETED.getCode()))).thenReturn(
                lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.eq(any(), anyString())).thenReturn(
                lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.list()).thenReturn(Collections.emptyList());

        List<UsUser> dbUserList = new ArrayList<>();
        UsUser dbUser = new UsUser();
        dbUser.setAadId("test-aad-id");
        dbUser.setMainDepartment(1);
        dbUser.setDeleted(NOT_DELETED.getCode());
        dbUserList.add(dbUser);

        LambdaQueryChainWrapper<UsUser> lambdaQueryChainWrapperUsUser = mock(LambdaQueryChainWrapper.class);
        when(usUserService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.in(any(), anyList())).thenReturn(lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.eq(any(), eq(NOT_DELETED.getCode()))).thenReturn(
                lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.list()).thenReturn(dbUserList);

        var usDepartmentUser = new UsDepartmentUser();
        usDepartmentUser.setSourceDeptId("L8-Dept2"); // 部门修改
        when(usDepartmentUserService.list(any(LambdaQueryWrapper.class))).thenReturn(List.of(usDepartmentUser));

        when(environment.getProperty(anyString())).thenReturn("test");
        when(templateEngine.process(anyString(), any(IContext.class))).thenReturn("模拟的邮件内容");

        // Act
        wechatActiveStaffinfoServiceImpl.syncStaffInfo();

        // Assert
        verify(wechatOrgchartService, times(1)).lambdaQuery();
        verify(wechatStaffDTOService, times(1)).lambdaQuery();
        verify(usDepartmentService, times(3)).lambdaQuery();
        verify(usUserService, times(1)).lambdaQuery();
        verify(messageClient, times(2)).sendWithReply(any(EmailSendCommandVo.class));
    }

    @Test
    @DisplayName("同步员工信息-正常流程：修改场景，用户字段修改")
    void syncStaffInfo_update_userInfo_normalFlow() {
        // Arrange
        mockWechatOrgchart();

        List<WechatStuffDTO> wechatStuffDTOList = new ArrayList<>();
        var wechatStuffDTO = buildWechatStuffDTO();
        wechatStuffDTOList.add(wechatStuffDTO);

        LambdaQueryChainWrapper<WechatStuffDTO> lambdaQueryChainWrapperWechatStuffDTO =
                mock(LambdaQueryChainWrapper.class);
        when(wechatStaffDTOService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.in(any(), anyList())).thenReturn(
                lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.isNotNull(any())).thenReturn(lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.list()).thenReturn(wechatStuffDTOList);

        List<UsDepartment> usDepartmentList = new ArrayList<>();
        UsDepartment usDepartment = new UsDepartment();
        usDepartment.setSourceDeptId("L8-Dept1");
        usDepartment.setTargetDeptId("1");
        usDepartment.setDeleted(NOT_DELETED.getCode());
        usDepartmentList.add(usDepartment);

        LambdaQueryChainWrapper<UsDepartment> lambdaQueryChainWrapperUsDepartment = mock(LambdaQueryChainWrapper.class);
        when(usDepartmentService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.in(any(), anyList())).thenReturn(lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.eq(any(), eq(NOT_DELETED.getCode()))).thenReturn(
                lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.eq(any(), anyString())).thenReturn(
                lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.list()).thenReturn(usDepartmentList);

        UsUser dbUserLineManager = new UsUser();
        dbUserLineManager.setAadId(wechatStuffDTO.getSupervisorAadId());
        dbUserLineManager.setSourceUserid(wechatStuffDTO.getSupervisorAdUsername());
        dbUserLineManager.setMainDepartment(1);
        dbUserLineManager.setDataStatus(DataStatusEnum.UPDATE.getCode());
        dbUserLineManager.setSyncStatus(SyncStatusEnum.SUCCESS.getCode());
        dbUserLineManager.setDeleted(NOT_DELETED.getCode());

        Text text = new Text();
        text.setValue(wechatStuffDTO.getChineseTitle() + "xxx"); // 中文职位变更
        Attrs attrs = new Attrs();
        attrs.setText(text);
        Extattr dbExtattr = new Extattr();
        dbExtattr.setAttrs(List.of(attrs));

        UsUser dbUser = new UsUser();
        dbUser.setAadId("test-aad-id");
        dbUser.setMainDepartment(1);
        dbUser.setDirectLeader(dbUserLineManager.getAadId() + "xxx"); // 直接领导变更
        dbUser.setExtattr(JSON.toJSONString(dbExtattr));
        dbUser.setDeleted(NOT_DELETED.getCode());
        List<UsUser> dbUserList = List.of(dbUser, dbUserLineManager);

        LambdaQueryChainWrapper<UsUser> lambdaQueryChainWrapperUsUser = mock(LambdaQueryChainWrapper.class);
        when(usUserService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.in(any(), anyList())).thenReturn(lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.eq(any(), eq(NOT_DELETED.getCode()))).thenReturn(
                lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.list()).thenReturn(dbUserList);

        var usDepartmentUser = new UsDepartmentUser();
        usDepartmentUser.setSourceDeptId(wechatStuffDTO.getDepartment()); // 部门相同
        when(usDepartmentUserService.list(any(LambdaQueryWrapper.class))).thenReturn(List.of(usDepartmentUser));

        // Act
        wechatActiveStaffinfoServiceImpl.syncStaffInfo();

        // Assert
        verify(wechatOrgchartService, times(1)).lambdaQuery();
        verify(wechatStaffDTOService, times(1)).lambdaQuery();
        verify(usDepartmentService, times(2)).lambdaQuery();
        verify(usUserService, times(1)).lambdaQuery();
    }

    @Test
    @DisplayName("同步员工信息-正常流程：新增场景")
    void syncStaffInfo_insert_userInfo_normalFlow() {
        // Arrange
        mockWechatOrgchart();

        List<WechatStuffDTO> wechatStuffDTOList = new ArrayList<>();
        var wechatStuffDTO = buildWechatStuffDTO();
        wechatStuffDTO.setDepartment("L8-Dept1");
        wechatStuffDTOList.add(wechatStuffDTO);

        LambdaQueryChainWrapper<WechatStuffDTO> lambdaQueryChainWrapperWechatStuffDTO =
                mock(LambdaQueryChainWrapper.class);
        when(wechatStaffDTOService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.in(any(), anyList())).thenReturn(
                lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.isNotNull(any())).thenReturn(lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.list()).thenReturn(wechatStuffDTOList);

        List<UsDepartment> usDepartmentList = new ArrayList<>();
        UsDepartment usDepartment = new UsDepartment();
        usDepartment.setSourceDeptId(wechatStuffDTO.getDepartment());
        usDepartment.setTargetDeptId("1");
        usDepartment.setDeleted(NOT_DELETED.getCode());
        usDepartmentList.add(usDepartment);

        when(usDepartmentService.list()).thenReturn(usDepartmentList);

        LambdaQueryChainWrapper<UsDepartment> lambdaQueryChainWrapperUsDepartment = mock(LambdaQueryChainWrapper.class);
        when(usDepartmentService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.in(any(), anyList())).thenReturn(lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.eq(any(), eq(NOT_DELETED.getCode()))).thenReturn(
                lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.list()).thenReturn(usDepartmentList);

        UsUser dbUserLineManager = new UsUser();
        dbUserLineManager.setAadId(wechatStuffDTO.getSupervisorAadId());
        dbUserLineManager.setSourceUserid(wechatStuffDTO.getSupervisorAdUsername());
        dbUserLineManager.setMainDepartment(1);
        dbUserLineManager.setDataStatus(DataStatusEnum.UPDATE.getCode());
        dbUserLineManager.setSyncStatus(SyncStatusEnum.SUCCESS.getCode());
        dbUserLineManager.setDeleted(NOT_DELETED.getCode());
        List<UsUser> dbUserList = List.of(dbUserLineManager);

        LambdaQueryChainWrapper<UsUser> lambdaQueryChainWrapperUsUser = mock(LambdaQueryChainWrapper.class);
        when(usUserService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.in(any(), anyList())).thenReturn(lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.eq(any(), eq(NOT_DELETED.getCode()))).thenReturn(
                lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.list()).thenReturn(dbUserList);

        // Act
        wechatActiveStaffinfoServiceImpl.syncStaffInfo();

        // Assert
        verify(wechatOrgchartService, times(1)).lambdaQuery();
        verify(wechatStaffDTOService, times(1)).lambdaQuery();
        verify(usDepartmentService, times(1)).lambdaQuery();
        verify(usUserService, times(1)).lambdaQuery();
    }


    @Test
    @DisplayName("同步员工信息-正常流程：新增场景，存在大量信字段为null")
    void syncStaffInfo_insert_userInfo_fields_null_normalFlow() {
        // Arrange
        mockWechatOrgchart();

        List<WechatStuffDTO> wechatStuffDTOList = new ArrayList<>();
        var wechatStuffDTO = new WechatStuffDTO();
        wechatStuffDTO.setAdUsername("test-user");
        wechatStuffDTO.setAadId("test-aad-id");
        wechatStuffDTO.setDepartment("L8-Dept1");
        wechatStuffDTO.setDeleted(NOT_DELETED.getCode());
        wechatStuffDTOList.add(wechatStuffDTO);

        LambdaQueryChainWrapper<WechatStuffDTO> lambdaQueryChainWrapperWechatStuffDTO =
                mock(LambdaQueryChainWrapper.class);
        when(wechatStaffDTOService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.in(any(), anyList())).thenReturn(
                lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.isNotNull(any())).thenReturn(lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.list()).thenReturn(wechatStuffDTOList);

        List<UsDepartment> usDepartmentList = new ArrayList<>();
        UsDepartment usDepartment = new UsDepartment();
        usDepartment.setSourceDeptId(wechatStuffDTO.getDepartment());
        usDepartment.setTargetDeptId("1");
        usDepartment.setDeleted(NOT_DELETED.getCode());
        usDepartmentList.add(usDepartment);

        when(usDepartmentService.list()).thenReturn(usDepartmentList);

        LambdaQueryChainWrapper<UsDepartment> lambdaQueryChainWrapperUsDepartment = mock(LambdaQueryChainWrapper.class);
        when(usDepartmentService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.in(any(), anyList())).thenReturn(lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.eq(any(), eq(NOT_DELETED.getCode()))).thenReturn(
                lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.list()).thenReturn(usDepartmentList);

        UsUser dbUserLineManager = new UsUser();
        dbUserLineManager.setAadId(wechatStuffDTO.getSupervisorAadId());
        dbUserLineManager.setSourceUserid(wechatStuffDTO.getSupervisorAdUsername());
        dbUserLineManager.setMainDepartment(1);
        dbUserLineManager.setDataStatus(DataStatusEnum.UPDATE.getCode());
        dbUserLineManager.setSyncStatus(SyncStatusEnum.SUCCESS.getCode());
        dbUserLineManager.setDeleted(NOT_DELETED.getCode());
        List<UsUser> dbUserList = List.of(dbUserLineManager);

        LambdaQueryChainWrapper<UsUser> lambdaQueryChainWrapperUsUser = mock(LambdaQueryChainWrapper.class);
        when(usUserService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.in(any(), anyList())).thenReturn(lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.eq(any(), eq(NOT_DELETED.getCode()))).thenReturn(
                lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.list()).thenReturn(dbUserList);

        // Act
        wechatActiveStaffinfoServiceImpl.syncStaffInfo();

        // Assert
        verify(wechatOrgchartService, times(1)).lambdaQuery();
        verify(wechatStaffDTOService, times(1)).lambdaQuery();
        verify(usDepartmentService, times(1)).lambdaQuery();
        verify(usUserService, times(1)).lambdaQuery();
    }

    @Test
    @DisplayName("同步员工信息-正常流程：新增场景，英文名（别名），手机，邮箱，部门不合法")
    void syncStaffInfo_insert_invalid_alias_phone_emali_department_normalFlow() {
        // Arrange
        mockWechatOrgchart();

        List<WechatStuffDTO> wechatStuffDTOList = new ArrayList<>();
        var wechatStuffDTO = new WechatStuffDTO();
        wechatStuffDTO.setAdUsername("test-user");
        wechatStuffDTO.setAadId("test-aad-id");
        wechatStuffDTO.setDepartment("L8-Dept1");
        wechatStuffDTO.setBusinessPhone("234LDFGF"); // 设置为不合法座机号
        wechatStuffDTO.setStaffEnglishName(randomString(65)); // 设置为超过限制长度
        wechatStuffDTO.setEmailAddressKpl("invalid-email"); // 设置为无效邮箱
        wechatStuffDTO.setDeleted(NOT_DELETED.getCode());
        wechatStuffDTOList.add(wechatStuffDTO);

        LambdaQueryChainWrapper<WechatStuffDTO> lambdaQueryChainWrapperWechatStuffDTO =
                mock(LambdaQueryChainWrapper.class);
        when(wechatStaffDTOService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.in(any(), anyList())).thenReturn(
                lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.isNotNull(any())).thenReturn(lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.list()).thenReturn(wechatStuffDTOList);

        List<UsDepartment> usDepartmentList = new ArrayList<>();
        UsDepartment usDepartment = new UsDepartment();
        usDepartment.setSourceDeptId(wechatStuffDTO.getDepartment() + "XXX"); // 当前只有非用户所在部门
        usDepartment.setTargetDeptId("1");
        usDepartment.setDeleted(NOT_DELETED.getCode());
        usDepartmentList.add(usDepartment);

        LambdaQueryChainWrapper<UsDepartment> lambdaQueryChainWrapperUsDepartment = mock(LambdaQueryChainWrapper.class);
        when(usDepartmentService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.in(any(), anyList())).thenReturn(lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.eq(any(), eq(NOT_DELETED.getCode()))).thenReturn(
                lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.list()).thenReturn(usDepartmentList);

        when(usDepartmentService.list()).thenReturn(usDepartmentList);

        when(environment.getProperty(anyString())).thenReturn("test");

        UsUser dbUserLineManager = new UsUser();
        dbUserLineManager.setAadId(wechatStuffDTO.getSupervisorAadId());
        dbUserLineManager.setSourceUserid(wechatStuffDTO.getSupervisorAdUsername());
        dbUserLineManager.setMainDepartment(1);
        dbUserLineManager.setDataStatus(DataStatusEnum.UPDATE.getCode());
        dbUserLineManager.setSyncStatus(SyncStatusEnum.SUCCESS.getCode());
        dbUserLineManager.setDeleted(NOT_DELETED.getCode());
        List<UsUser> dbUserList = List.of(dbUserLineManager);

        LambdaQueryChainWrapper<UsUser> lambdaQueryChainWrapperUsUser = mock(LambdaQueryChainWrapper.class);
        when(usUserService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.in(any(), anyList())).thenReturn(lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.eq(any(), eq(NOT_DELETED.getCode()))).thenReturn(
                lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.list()).thenReturn(dbUserList);

        when(environment.getProperty(anyString())).thenReturn("test");
        when(templateEngine.process(anyString(), any(IContext.class))).thenReturn("模拟的邮件内容");

        // Act
        wechatActiveStaffinfoServiceImpl.syncStaffInfo();

        // Assert
        verify(wechatOrgchartService, times(1)).lambdaQuery();
        verify(wechatStaffDTOService, times(1)).lambdaQuery();
        verify(usDepartmentService, times(1)).lambdaQuery();
        verify(usUserService, times(1)).lambdaQuery();
        verify(messageClient, times(1)).sendWithReply(any(EmailSendCommandVo.class));
    }


    @Test
    @DisplayName("同步员工信息-用户被删除的分支")
    void syncStaffInfo_userDeletedBranch() {
        // Arrange
        // 模拟部门白名单
        when(jiaLiConfig.getWhiteDeptL2()).thenReturn(List.of("L2-Dept1"));

        // 模拟组织架构查询结果
        List<WechatOrgchart> orgchartList = new ArrayList<>();
        WechatOrgchart orgchart = new WechatOrgchart();
        orgchart.setL2DivisionCode("L2-Dept1");
        orgchart.setL8DepartmentCode("L8-Dept1");
        orgchartList.add(orgchart);

        LambdaQueryChainWrapper<WechatOrgchart> lambdaQueryChainWrapperWechatOrgchart =
                mock(LambdaQueryChainWrapper.class);
        when(wechatOrgchartService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperWechatOrgchart);
        when(lambdaQueryChainWrapperWechatOrgchart.in(any(), anyList())).thenReturn(
                lambdaQueryChainWrapperWechatOrgchart);
        when(lambdaQueryChainWrapperWechatOrgchart.list()).thenReturn(orgchartList);

        // 模拟员工信息
        List<WechatStuffDTO> wechatStuffDTOList = new ArrayList<>();
        WechatStuffDTO wechatStuffDTO = new WechatStuffDTO();
        wechatStuffDTO.setAadId("test-aad-id");
        wechatStuffDTO.setAdUsername("test-user");
        wechatStuffDTO.setDeleted(DeleteStatusEnum.DELETED.getCode()); // 设置为已删除
        wechatStuffDTOList.add(wechatStuffDTO);

        LambdaQueryChainWrapper<WechatStuffDTO> lambdaQueryChainWrapperWechatStuffDTO =
                mock(LambdaQueryChainWrapper.class);
        when(wechatStaffDTOService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.in(any(), anyList())).thenReturn(
                lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.isNotNull(any())).thenReturn(lambdaQueryChainWrapperWechatStuffDTO);
        when(lambdaQueryChainWrapperWechatStuffDTO.list()).thenReturn(wechatStuffDTOList);

        // 模拟数据库中已有的用户
        List<UsUser> dbUserList = new ArrayList<>();
        UsUser dbUser = new UsUser();
        dbUser.setAadId(wechatStuffDTO.getAadId());
        dbUser.setDeleted(DeleteStatusEnum.NOT_DELETED.getCode()); // 数据库中未删除

        UsUser dbUserInsertFailed = new UsUser();
        dbUserInsertFailed.setAadId("test-aad-id-2");
        dbUserInsertFailed.setDataStatus(DataStatusEnum.INSERT.getCode());
        dbUserInsertFailed.setSyncStatus(SyncStatusEnum.FAIL.getCode());
        dbUserInsertFailed.setDeleted(DeleteStatusEnum.NOT_DELETED.getCode()); // 数据库中未删除
        dbUserList.add(dbUser);
        dbUserList.add(dbUserInsertFailed);

        LambdaQueryChainWrapper<UsUser> lambdaQueryChainWrapperUsUser = mock(LambdaQueryChainWrapper.class);
        when(usUserService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.in(any(), anyList())).thenReturn(lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.eq(any(), eq(DeleteStatusEnum.NOT_DELETED.getCode()))).thenReturn(
                lambdaQueryChainWrapperUsUser);
        when(lambdaQueryChainWrapperUsUser.list()).thenReturn(dbUserList);

        List<UsDepartment> usDepartmentList = new ArrayList<>();
        UsDepartment usDepartment = new UsDepartment();
        usDepartment.setSourceDeptId(wechatStuffDTO.getDepartment());
        usDepartment.setTargetDeptId("1");
        usDepartment.setDeleted(NOT_DELETED.getCode());
        usDepartmentList.add(usDepartment);

        when(usDepartmentService.list()).thenReturn(usDepartmentList);

        LambdaQueryChainWrapper<UsDepartment> lambdaQueryChainWrapperUsDepartment = mock(LambdaQueryChainWrapper.class);
        when(usDepartmentService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.in(any(), anyList())).thenReturn(lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.eq(any(), eq(NOT_DELETED.getCode()))).thenReturn(
                lambdaQueryChainWrapperUsDepartment);
        when(lambdaQueryChainWrapperUsDepartment.list()).thenReturn(usDepartmentList);

        // Act
        wechatActiveStaffinfoServiceImpl.syncStaffInfo();

        // Assert
        // 验证用户被标记为删除
        assertEquals(DataStatusEnum.DELETE.getCode(), dbUser.getDataStatus(), "用户状态应被标记为删除");
        assertEquals(SyncStatusEnum.INIT.getCode(), dbUser.getSyncStatus(), "同步状态应为INIT");
        verify(usUserService, times(1)).lambdaQuery();
    }

    @Test
    @DisplayName("同步员工信息-部门白名单为空")
    void syncStaffInfo_emptyWhiteDeptList() {
        // Arrange
        when(jiaLiConfig.getWhiteDeptL2()).thenReturn(Collections.emptyList());

        LambdaQueryChainWrapper<WechatOrgchart> lambdaQueryChainWrapperWechatOrgchart =
                mock(LambdaQueryChainWrapper.class);
        when(wechatOrgchartService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperWechatOrgchart);
        when(lambdaQueryChainWrapperWechatOrgchart.in(any(), anyList())).thenReturn(
                lambdaQueryChainWrapperWechatOrgchart);
        when(lambdaQueryChainWrapperWechatOrgchart.list()).thenReturn(Collections.emptyList());

        // Act
        wechatActiveStaffinfoServiceImpl.syncStaffInfo();

        // Assert
        verify(wechatOrgchartService).lambdaQuery();
        verify(wechatStaffDTOService, never()).lambdaQuery();
        verify(usDepartmentService, never()).lambdaQuery();
        verify(usUserService, never()).lambdaQuery();
    }

    @Test
    @DisplayName("同步员工信息-异常场景")
    void syncStaffInfo_exceptionScenario() {
        // Arrange
        List<String> whiteDeptL2 = new ArrayList<>();
        whiteDeptL2.add("L2-Dept1");
        when(jiaLiConfig.getWhiteDeptL2()).thenReturn(whiteDeptL2);

        LambdaQueryChainWrapper<WechatOrgchart> lambdaQueryChainWrapperWechatOrgchart =
                mock(LambdaQueryChainWrapper.class);
        when(wechatOrgchartService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperWechatOrgchart);
        when(lambdaQueryChainWrapperWechatOrgchart.in(eq(WechatOrgchart::getL2DivisionCode), anyList())).thenReturn(
                lambdaQueryChainWrapperWechatOrgchart);
        when(lambdaQueryChainWrapperWechatOrgchart.list()).thenThrow(new RuntimeException("Mocked Exception"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> wechatActiveStaffinfoServiceImpl.syncStaffInfo());
        verify(wechatOrgchartService, times(1)).lambdaQuery();
    }

    @Test
    @DisplayName("手动禁用员工-正常场景: 用户状态为新增失败")
    void manuallyDisableStaff_user_insert_failed_success() {
        // Arrange
        String adUserName = "test-user";
        UsUser usUser = new UsUser();
        usUser.setSourceUserid(adUserName);
        usUser.setDataStatus(DataStatusEnum.INSERT.getCode());
        usUser.setSyncStatus(SyncStatusEnum.FAIL.getCode());
        usUser.setEnable(1); // 启用状态

        LambdaQueryChainWrapper<UsUser> mockLambdaQuery = mock(LambdaQueryChainWrapper.class);
        when(usUserService.lambdaQuery()).thenReturn(mockLambdaQuery);
        when(mockLambdaQuery.eq(any(), anyString())).thenReturn(mockLambdaQuery);
        when(mockLambdaQuery.eq(any(), anyInt())).thenReturn(mockLambdaQuery);
        when(mockLambdaQuery.list()).thenReturn(Collections.singletonList(usUser));

        when(usUserService.updateById(any(UsUser.class))).thenReturn(true);

        // Act
        UsUser result = wechatActiveStaffinfoServiceImpl.manuallyDisableStaff(adUserName);

        // Assert
        assertEquals(0, result.getEnable(), "员工状态应为禁用");
        verify(usUserService, times(1)).updateById(any(UsUser.class));
    }

    @Test
    @DisplayName("手动禁用员工-正常场景: 用户状态为更新")
    void manuallyDisableStaff_user_state_updatesuccess() {
        // Arrange
        String adUserName = "test-user";
        UsUser usUser = new UsUser();
        usUser.setSourceUserid(adUserName);
        usUser.setDataStatus(DataStatusEnum.UPDATE.getCode());
        usUser.setEnable(1); // 启用状态

        LambdaQueryChainWrapper<UsUser> mockLambdaQuery = mock(LambdaQueryChainWrapper.class);
        when(usUserService.lambdaQuery()).thenReturn(mockLambdaQuery);
        when(mockLambdaQuery.eq(any(), anyString())).thenReturn(mockLambdaQuery);
        when(mockLambdaQuery.eq(any(), anyInt())).thenReturn(mockLambdaQuery);
        when(mockLambdaQuery.list()).thenReturn(Collections.singletonList(usUser));

        when(usUserService.updateById(any(UsUser.class))).thenReturn(true);

        // Act
        UsUser result = wechatActiveStaffinfoServiceImpl.manuallyDisableStaff(adUserName);

        // Assert
        assertEquals(0, result.getEnable(), "员工状态应为禁用");
        verify(usUserService, times(1)).updateById(any(UsUser.class));
    }

    @Test
    @DisplayName("手动退出员工-正常场景：用户状态为未删除")
    void manuallyExitedStaff_user_not_deleted_success() {
        // Arrange
        String adUserName = "test-user";
        UsUser usUser = new UsUser();
        usUser.setSourceUserid(adUserName);
        usUser.setDataStatus(DataStatusEnum.INSERT.getCode());
        usUser.setEnable(ENABLE.getCode()); // 启用状态

        LambdaQueryChainWrapper<UsUser> mockLambdaQuery = mock(LambdaQueryChainWrapper.class);
        when(usUserService.lambdaQuery()).thenReturn(mockLambdaQuery);
        when(mockLambdaQuery.eq(any(), anyString())).thenReturn(mockLambdaQuery);
        when(mockLambdaQuery.eq(any(), anyInt())).thenReturn(mockLambdaQuery);
        when(mockLambdaQuery.list()).thenReturn(Collections.singletonList(usUser));

        when(usUserService.updateById(any(UsUser.class))).thenReturn(true);

        // Act
        UsUser result = wechatActiveStaffinfoServiceImpl.manuallyExitedStaff(adUserName);

        // Assert
        assertEquals(DataStatusEnum.DELETE.getCode(), result.getDataStatus());
        assertEquals(SyncStatusEnum.INIT.getCode(), result.getSyncStatus());
        verify(usUserService, times(1)).updateById(any(UsUser.class));
    }

    @Test
    @DisplayName("手动退出员工-正常场景：用户状态为已删除")
    void manuallyExitedStaff_user_deleted_success() {
        // Arrange
        String adUserName = "test-user";
        UsUser usUser = new UsUser();
        usUser.setSourceUserid(adUserName);
        usUser.setDataStatus(DataStatusEnum.DELETE.getCode());
        usUser.setEnable(ENABLE.getCode()); // 启用状态

        LambdaQueryChainWrapper<UsUser> mockLambdaQuery = mock(LambdaQueryChainWrapper.class);
        when(usUserService.lambdaQuery()).thenReturn(mockLambdaQuery);
        when(mockLambdaQuery.eq(any(), anyString())).thenReturn(mockLambdaQuery);
        when(mockLambdaQuery.eq(any(), anyInt())).thenReturn(mockLambdaQuery);
        when(mockLambdaQuery.list()).thenReturn(Collections.singletonList(usUser));

        // Act
        UsUser result = wechatActiveStaffinfoServiceImpl.manuallyExitedStaff(adUserName);

        // Assert
        assertEquals(DataStatusEnum.DELETE.getCode(), result.getDataStatus(), "员工状态应为删除");
        verify(usUserService, never()).updateById(any(UsUser.class));
    }


    @Test
    @DisplayName("批量更新数据到数据库-异常场景：新增员工失败")
    void batchUpdateToDB_addUserException() {
        // Arrange
        List<UsUser> addUsUserList = new ArrayList<>();
        UsUser user = new UsUser();
        user.setSourceUserid("test-user");
        addUsUserList.add(user);

        List<UsUser> updateUsUserList = new ArrayList<>();
        List<UsDepartmentUser> addUsDepartmentUserList = new ArrayList<>();
        List<UsDepartmentUser> updateUsDepartmentUserList = new ArrayList<>();

        doThrow(new RuntimeException("Mocked Exception")).when(usUserService)
                                                         .save(any());

        // Act & Assert
        assertDoesNotThrow(() -> {
            ReflectionTestUtils.invokeMethod(wechatActiveStaffinfoServiceImpl, "batchUpdateToDB", addUsUserList,
                                             updateUsUserList, addUsDepartmentUserList, updateUsDepartmentUserList);
        });

        // Assert
        verify(jialiUsSyncLogService, times(1)).saveBatch(anyList());
    }

    @Test
    @DisplayName("批量更新数据到数据库-异常场景：更新员工失败")
    void batchUpdateToDB_updateUserException() {
        // Arrange
        List<UsUser> addUsUserList = Collections.emptyList();
        List<UsUser> updateUsUserList = new ArrayList<>();
        UsUser user = new UsUser();
        user.setSourceUserid("test-user");
        updateUsUserList.add(user);

        List<UsDepartmentUser> addUsDepartmentUserList = new ArrayList<>();
        List<UsDepartmentUser> updateUsDepartmentUserList = new ArrayList<>();

        doThrow(new RuntimeException("Mocked Exception")).when(usUserMapper)
                                                         .updateUsUser(any());

        // Act & Assert
        assertDoesNotThrow(() -> {
            ReflectionTestUtils.invokeMethod(wechatActiveStaffinfoServiceImpl, "batchUpdateToDB", addUsUserList,
                                             updateUsUserList, addUsDepartmentUserList, updateUsDepartmentUserList);
        });

        // Assert
        verify(jialiUsSyncLogService, times(1)).saveBatch(anyList());
    }

    @Test
    @DisplayName("批量更新数据到数据库-异常场景：新增部门员工关系失败")
    void batchUpdateToDB_addDepartmentUserException() {
        // Arrange
        List<UsUser> addUsUserList = new ArrayList<>();
        List<UsUser> updateUsUserList = new ArrayList<>();
        List<UsDepartmentUser> addUsDepartmentUserList = new ArrayList<>();
        UsDepartmentUser departmentUser = new UsDepartmentUser();
        departmentUser.setTartgetUserid("test-user");
        addUsDepartmentUserList.add(departmentUser);

        List<UsDepartmentUser> updateUsDepartmentUserList = new ArrayList<>();

        doThrow(new RuntimeException("Mocked Exception")).when(usDepartmentUserService)
                                                         .save(any());

        // Act & Assert
        assertDoesNotThrow(() -> {
            ReflectionTestUtils.invokeMethod(wechatActiveStaffinfoServiceImpl, "batchUpdateToDB", addUsUserList,
                                             updateUsUserList, addUsDepartmentUserList, updateUsDepartmentUserList);
        });

        // Assert
        verify(jialiUsSyncLogService, times(1)).saveBatch(anyList());
    }

    @Test
    @DisplayName("批量更新数据到数据库-异常场景：更新部门员工关系失败")
    void batchUpdateToDB_updateDepartmentUserException() {
        // Arrange
        List<UsUser> addUsUserList = new ArrayList<>();
        List<UsUser> updateUsUserList = new ArrayList<>();
        List<UsDepartmentUser> addUsDepartmentUserList = new ArrayList<>();
        List<UsDepartmentUser> updateUsDepartmentUserList = new ArrayList<>();
        UsDepartmentUser departmentUser = new UsDepartmentUser();
        departmentUser.setTartgetUserid("test-user");
        updateUsDepartmentUserList.add(departmentUser);

        doThrow(new RuntimeException("Mocked Exception")).when(usDepartmentUserService)
                                                         .updateById(any());

        // Act & Assert
        assertDoesNotThrow(() -> {
            ReflectionTestUtils.invokeMethod(wechatActiveStaffinfoServiceImpl, "batchUpdateToDB", addUsUserList,
                                             updateUsUserList, addUsDepartmentUserList, updateUsDepartmentUserList);
        });

        // Assert
        verify(jialiUsSyncLogService, times(1)).saveBatch(anyList());
    }

    private void mockWechatOrgchart() {
        when(jiaLiConfig.getWhiteDept()).thenReturn(List.of("L2-Dept1"));

        List<String> whiteDeptL2 = new ArrayList<>();
        whiteDeptL2.add("L2-Dept1");
        when(jiaLiConfig.getWhiteDeptL2()).thenReturn(whiteDeptL2);

        List<WechatOrgchart> orgchartList = new ArrayList<>();
        WechatOrgchart orgchart = new WechatOrgchart();
        orgchart.setL2DivisionCode("L2-Dept1");
        orgchartList.add(orgchart);

        LambdaQueryChainWrapper<WechatOrgchart> lambdaQueryChainWrapperWechatOrgchart =
                mock(LambdaQueryChainWrapper.class);
        when(wechatOrgchartService.lambdaQuery()).thenReturn(lambdaQueryChainWrapperWechatOrgchart);
        when(lambdaQueryChainWrapperWechatOrgchart.in(any(), anyList())).thenReturn(
                lambdaQueryChainWrapperWechatOrgchart);
        when(lambdaQueryChainWrapperWechatOrgchart.list()).thenReturn(orgchartList);
    }

    private WechatStuffDTO buildWechatStuffDTO() {
        var wechatStuffDTO = new WechatStuffDTO();
        wechatStuffDTO.setAdUsername("test-user");
        wechatStuffDTO.setAadId("test-aad-id");
        wechatStuffDTO.setDepartment("L8-Dept1");
        wechatStuffDTO.setStaffNo("S12345");
        wechatStuffDTO.setEnglishTitle("Software Engineer");
        wechatStuffDTO.setChineseTitle("软件工程师");
        wechatStuffDTO.setMobilePhone("1234567890");
        wechatStuffDTO.setEmailAddressKpl("<EMAIL>");
        wechatStuffDTO.setDepartment("IT Department");
        wechatStuffDTO.setBusinessPhone("0987654321");
        wechatStuffDTO.setStaffEnglishName("John Doe");
        wechatStuffDTO.setStaffChineseName("约翰·多");
        wechatStuffDTO.setGender("Male");
        wechatStuffDTO.setSupervisorStaffno("S54321");
        wechatStuffDTO.setSupervisorAadId("supervisor-aad-id");
        wechatStuffDTO.setSupervisorAdUsername("supervisor");
        wechatStuffDTO.setCreateTime(new Date());
        wechatStuffDTO.setUpdateTime(new Date());
        wechatStuffDTO.setEnabled(1);
        wechatStuffDTO.setDeleted(NOT_DELETED.getCode());
        return wechatStuffDTO;
    }

}