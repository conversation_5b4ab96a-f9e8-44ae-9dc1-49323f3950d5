package com.wshoto.jiali.service.impl;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * 数据库管理类-单元测试
 *
 * <AUTHOR> Yan
 * @since 2025-5-19
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("数据库管理类-单元测试")
class DbManagerServiceImplTest {

    @Mock(name = "dataSource")
    private DynamicRoutingDataSource dataSource;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private DbManagerServiceImpl dbManagerServiceImpl;

    @Test
    @DisplayName("获取所有数据库-正常场景")
    void getDatabases_success() {
        // Arrange
        DataSource mockDataSource = mock(DataSource.class);
        when(dataSource.getDataSources()).thenReturn(Map.of("db1", mockDataSource, "db2", mockDataSource));

        // Act
        List<String> result = dbManagerServiceImpl.getDatabases();

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("db1"));
        assertTrue(result.contains("db2"));
    }

    @Test
    @DisplayName("获取指定数据库的表-正常场景")
    void getTables_success() {
        // Arrange
        String dbName = "test_db";
        List<String> mockTables = List.of("table1", "table2");
        when(jdbcTemplate.queryForList(anyString(), eq(String.class))).thenReturn(mockTables);

        // Act
        List<String> result = dbManagerServiceImpl.getTables(dbName);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("table1"));
        assertTrue(result.contains("table2"));
    }

    @Test
    @DisplayName("获取指定数据库的表-异常场景")
    void getTables_exception() {
        // Arrange
        String dbName = "test_db";
        when(jdbcTemplate.queryForList(anyString(), eq(String.class))).thenThrow(
                new RuntimeException("Mocked Exception"));

        // Act
        List<String> result = dbManagerServiceImpl.getTables(dbName);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("获取表结构-正常场景")
    void getTableStructure_success() {
        // Arrange
        String dbName = "test_db";
        String tableName = "test_table";
        List<Map<String, Object>> mockStructure =
                List.of(Map.of("column_name", "id", "data_type", "bigint", "is_nullable", "NO", "column_comment",
                               "Primary Key"),
                        Map.of("column_name", "name", "data_type", "varchar", "is_nullable", "YES", "column_comment",
                               "Name"));

        when(jdbcTemplate.queryForList(anyString(), eq(tableName))).thenReturn(mockStructure);

        // Act
        List<Map<String, Object>> result = dbManagerServiceImpl.getTableStructure(dbName, tableName);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("id", result.get(0)
                                 .get("column_name"));
        assertEquals("Primary Key", result.get(0)
                                          .get("column_comment"));
    }

    @Test
    @DisplayName("获取表结构-异常场景")
    void getTableStructure_exception() {
        // Arrange
        String dbName = "test_db";
        String tableName = "test_table";

        when(jdbcTemplate.queryForList(anyString(), eq(tableName))).thenThrow(new RuntimeException("Mocked Exception"));

        // Act
        List<Map<String, Object>> result = dbManagerServiceImpl.getTableStructure(dbName, tableName);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("获取表数据-正常场景")
    void getTableData_success() {
        // Arrange
        String dbName = "test_db";
        String tableName = "test_table";
        int page = 1;
        int size = 10;

        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class))).thenReturn(100L);
        when(jdbcTemplate.queryForList(anyString())).thenReturn(
                List.of(Map.of("id", 1, "name", "John"), Map.of("id", 2, "name", "Jane")));
        when(jdbcTemplate.queryForList(anyString(), eq(String.class), eq(tableName))).thenReturn(List.of("id", "name"));

        // Act
        Map<String, Object> result = dbManagerServiceImpl.getTableData(dbName, tableName, page, size);

        // Assert
        assertNotNull(result);
        assertEquals(100L, result.get("total"));
        assertEquals(2, ((List<?>) result.get("data")).size());
        assertEquals(List.of("id", "name"), result.get("columns"));
    }

    @Test
    @DisplayName("获取表数据-异常场景")
    void getTableData_exception() {
        // Arrange
        String dbName = "test_db";
        String tableName = "test_table";
        int page = 1;
        int size = 10;

        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class))).thenThrow(
                new RuntimeException("Mocked Exception"));

        // Act
        Map<String, Object> result = dbManagerServiceImpl.getTableData(dbName, tableName, page, size);

        // Assert
        assertNotNull(result);
        assertTrue((Boolean) result.get("error"));
        assertEquals(0, result.get("total"));
        assertTrue(((List<?>) result.get("data")).isEmpty());
    }

    @Test
    @DisplayName("执行SQL-正常场景：SELECT查询")
    void executeSql_selectQuery_success() {
        // Arrange
        String dbName = "test_db";
        String sql = "select * from test_table";
        List<Map<String, Object>> mockData = List.of(Map.of("id", 1, "name", "John"), Map.of("id", 2, "name", "Jane"));

        when(jdbcTemplate.queryForList(sql)).thenReturn(mockData);

        // Act
        Map<String, Object> result = dbManagerServiceImpl.executeSql(dbName, sql);

        // Assert
        assertNotNull(result);
        assertTrue((Boolean) result.get("success"));
        assertEquals(2, ((List<?>) result.get("data")).size());
    }

    @Test
    @DisplayName("执行SQL-正常场景：UPDATE操作")
    void executeSql_updateQuery_success() {
        // Arrange
        String dbName = "test_db";
        String sql = "update test_table set name = 'John' where id = 1";
        int rowsAffected = 1;

        when(jdbcTemplate.update(sql)).thenReturn(rowsAffected);

        // Act
        Map<String, Object> result = dbManagerServiceImpl.executeSql(dbName, sql);

        // Assert
        assertNotNull(result);
        assertTrue((Boolean) result.get("success"));
        assertEquals(1, result.get("rowsAffected"));
        assertEquals("UPDATE", result.get("type"));
    }

    @Test
    @DisplayName("执行SQL-异常场景：SQL执行失败")
    void executeSql_exception() {
        // Arrange
        String dbName = "test_db";
        String sql = "INVALID SQL";

        when(jdbcTemplate.update(anyString())).thenThrow(new RuntimeException("Mocked Exception"));

        // Act
        Map<String, Object> result = dbManagerServiceImpl.executeSql(dbName, sql);

        // Assert
        assertNotNull(result);
        assertFalse((Boolean) result.get("success"));
        assertTrue((Boolean) result.get("error"));
        assertEquals("Mocked Exception", result.get("message"));
    }

    @Test
    @DisplayName("更新表数据-正常场景")
    void updateData_success() {
        // Arrange
        String dbName = "test_db";
        String tableName = "test_table";
        String id = "1";
        Map<String, Object> data = new HashMap<>();
        data.put("name", "John");
        data.put("age", 30);
        data.put("group", "group1"); // 测试关键词需要转义
        data.put("number", null);
        data.put("gmt_create", "2025-12-01 23:00:00");
        data.put("deleted", "0");

        int updateCount = 1;
        String primaryKeyColumn = "id";
        when(jdbcTemplate.queryForObject(anyString(), eq(String.class), eq(tableName))).thenReturn(primaryKeyColumn);
        when(jdbcTemplate.update(anyString(), any(Object[].class))).thenReturn(updateCount);

        // Act
        Map<String, Object> result = dbManagerServiceImpl.updateData(dbName, tableName, id, data);

        // Assert
        assertNotNull(result);
        assertTrue((Boolean) result.get("success"));
        assertEquals(updateCount, result.get("rowsAffected"));
    }

    @Test
    @DisplayName("更新表数据-异常场景：无法确定主键列")
    void updateData_noPrimaryKey() {
        // Arrange
        String dbName = "test_db";
        String tableName = "test_table";
        String id = "1";
        Map<String, Object> data = new HashMap<>();
        data.put("name", "John");

        when(jdbcTemplate.queryForObject(anyString(), eq(String.class), eq(tableName))).thenReturn(null);

        // Act
        Map<String, Object> result = dbManagerServiceImpl.updateData(dbName, tableName, id, data);

        // Assert
        assertNotNull(result);
        assertFalse((Boolean) result.get("success"));
    }

    @Test
    @DisplayName("更新表数据-异常场景：SQL执行失败")
    void updateData_sqlExecutionFailure() {
        // Arrange
        String dbName = "test_db";
        String tableName = "test_table";
        String id = "1";
        Map<String, Object> data = new HashMap<>();
        data.put("name", "John");

        String primaryKeyColumn = "id";
        when(jdbcTemplate.queryForObject(anyString(), eq(String.class), eq(tableName))).thenReturn(primaryKeyColumn);
        when(jdbcTemplate.update(any(), any(Object[].class))).thenThrow(new RuntimeException("Mocked Exception"));

        // Act
        Map<String, Object> result = dbManagerServiceImpl.updateData(dbName, tableName, id, data);

        // Assert
        assertNotNull(result);
        assertFalse((Boolean) result.get("success"));
        assertEquals("Mocked Exception", result.get("message"));
    }

    @Test
    @DisplayName("删除表数据-正常场景")
    void deleteData_success() {
        // Arrange
        String dbName = "test_db";
        String tableName = "test_table";
        String id = "1";
        String primaryKeyColumn = "id";
        int rowsAffected = 1;

        when(jdbcTemplate.queryForObject(anyString(), eq(String.class), eq(tableName))).thenReturn(primaryKeyColumn);
        when(jdbcTemplate.update(anyString(), eq(id))).thenReturn(rowsAffected);

        // Act
        Map<String, Object> result = dbManagerServiceImpl.deleteData(dbName, tableName, id);

        // Assert
        assertNotNull(result);
        assertTrue((Boolean) result.get("success"));
        assertEquals(rowsAffected, result.get("rowsAffected"));
    }

    @Test
    @DisplayName("删除表数据-异常场景：无法确定主键列")
    void deleteData_noPrimaryKey() {
        // Arrange
        String dbName = "test_db";
        String tableName = "test_table";
        String id = "1";

        when(jdbcTemplate.queryForObject(anyString(), eq(String.class), eq(tableName))).thenReturn(null);

        // Act
        Map<String, Object> result = dbManagerServiceImpl.deleteData(dbName, tableName, id);

        // Assert
        assertNotNull(result);
        assertFalse((Boolean) result.get("success"));
    }

    @Test
    @DisplayName("删除表数据-异常场景：SQL执行失败")
    void deleteData_sqlExecutionFailure() {
        // Arrange
        String dbName = "test_db";
        String tableName = "test_table";
        String id = "1";
        String primaryKeyColumn = "id";

        when(jdbcTemplate.queryForObject(anyString(), eq(String.class), eq(tableName))).thenReturn(primaryKeyColumn);
        when(jdbcTemplate.update(anyString(), eq(id))).thenThrow(new RuntimeException("Mocked Exception"));

        // Act
        Map<String, Object> result = dbManagerServiceImpl.deleteData(dbName, tableName, id);

        // Assert
        assertNotNull(result);
        assertFalse((Boolean) result.get("success"));
    }

}