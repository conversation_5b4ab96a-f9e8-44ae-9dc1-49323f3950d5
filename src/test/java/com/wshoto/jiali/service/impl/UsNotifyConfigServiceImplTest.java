package com.wshoto.jiali.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.wshoto.jiali.domain.UsNotifyConfig;
import com.wshoto.jiali.mapper.UsNotifyConfigMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 微盛-通知配置-单元测试.
 *
 * <AUTHOR> @since 2025-5-20
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("微盛-通知配置-单元测试")
class UsNotifyConfigServiceImplTest {

    @Mock
    private UsNotifyConfigMapper usNotifyConfigMapper;

    @Spy
    @InjectMocks
    private UsNotifyConfigServiceImpl usNotifyConfigService;

    @Test
    @DisplayName("设置通知邮箱-正常场景")
    public void setNotifyEmails_success() {
        // Arrange
        String emails = "<EMAIL>";
        doReturn(usNotifyConfigMapper).when(usNotifyConfigService).getBaseMapper();
        when(usNotifyConfigMapper.setNotifyEmail(emails)).thenReturn(1);

        // Act
        int result = usNotifyConfigService.setNotifyEmails(emails);

        // Assert
        assertEquals(1, result);
        verify(usNotifyConfigMapper, times(1)).setNotifyEmail(emails);
    }

    @Test
    @DisplayName("删除通知配置-正常场景")
    public void deleteNotifyConfig_success() {
        // Arrange
        UsNotifyConfig config1 = new UsNotifyConfig();
        config1.setId(1L);
        UsNotifyConfig config2 = new UsNotifyConfig();
        config2.setId(2L);
        List<UsNotifyConfig> configs = List.of(config1, config2);

        LambdaQueryChainWrapper<UsNotifyConfig> queryChainWrapper = new LambdaQueryChainWrapper<>(usNotifyConfigMapper);
        when(usNotifyConfigService.lambdaQuery()).thenReturn(queryChainWrapper);
        when(queryChainWrapper.list()).thenReturn(configs);

        doReturn(usNotifyConfigMapper).when(usNotifyConfigService).getBaseMapper();

        // Act
        int result = usNotifyConfigService.deleteNotifyConfig();

        // Assert
        assertEquals(2, result);
        verify(usNotifyConfigMapper, times(1)).selectList(any());
        verify(usNotifyConfigMapper, times(2)).deleteById(anyLong());
    }
}