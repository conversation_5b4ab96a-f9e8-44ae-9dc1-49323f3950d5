package com.wshoto.jiali.service.impl;

import com.wshoto.jiali.config.JiaLiConfig;
import com.wshoto.jiali.domain.UsDepartment;
import com.wshoto.jiali.service.JialiUsSyncLogService;
import com.wshoto.jiali.service.UsDepartmentService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * 微盛-同步部门信息-单元测试
 *
 * <AUTHOR> Yan
 * @since 2025-5-20
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("微盛-同步部门信息-单元测试")
class WechatOrgchartServiceImplTest {

    @Mock
    private JiaLiConfig jiaLiConfig;

    @Mock
    private UsDepartmentService usDepartmentService;

    @Mock
    private JialiUsSyncLogService jialiUsSyncLogService;

    @InjectMocks
    private WechatOrgchartServiceImpl wechatOrgchartServiceImpl;

    @Test
    @DisplayName("批量新增和修改部门信息-异常场景：新增部门失败")
    void addAndUpdateBatchDept_addDeptException() {
        // Arrange
        List<UsDepartment> updateDeptList = new ArrayList<>();
        List<UsDepartment> addDeptList = new ArrayList<>();

        var deptAdd = new UsDepartment();
        deptAdd.setSourceDeptId("dept-1");
        deptAdd.setName("Test Departmen-1");
        addDeptList.add(deptAdd);

        var deptUpdate = new UsDepartment();
        deptUpdate.setSourceDeptId("dept-2");
        deptUpdate.setName("Test Department-2");
        updateDeptList.add(deptUpdate);

        doThrow(new RuntimeException("Mocked Exception")).when(usDepartmentService)
                                                         .save(any(UsDepartment.class));
        doThrow(new RuntimeException("Mocked Exception")).when(usDepartmentService)
                                                         .updateById(any(UsDepartment.class));

        // Act
        assertDoesNotThrow(() -> wechatOrgchartServiceImpl.addAndUpdateBatchDept(updateDeptList, addDeptList));

        // Assert
        verify(jialiUsSyncLogService, times(1)).saveBatch(anyList());
    }

    @ParameterizedTest
    @DisplayName("更新部门树-覆盖更新部门的所有分支")
    @CsvSource({"parent-1, code-1, New Name, New Name, Old Name, Old English Name, New Name, null",
            "parent-2, code-2, New English Name, , Old Name, Old English Name, New English Name, null",
            "parent-3, code-3, , New Chinese Name, Old Name, Old English Name, New Chinese Name, null",
            "parent-4, code-4, New English Name, New Chinese Name, Old Name, Old English Name, New Chinese Name, New " +
                    "English Name"
    })
    void updateDeptTree_updateBranches(String parentCode, String code, String enName, String cnName, String oldName,
                                       String oldEnName, String expectedName, String expectedEnName) throws Exception {
        // Arrange
        Map<String, UsDepartment> usDepartmentsMap = new HashMap<>();
        List<UsDepartment> addDeptList = new ArrayList<>();
        List<UsDepartment> updateDeptList = new ArrayList<>();

        UsDepartment existingDept = new UsDepartment();
        existingDept.setSourceDeptId(code);
        existingDept.setSourceDeptParentId(parentCode);
        existingDept.setName(oldName);
        existingDept.setNameEn(oldEnName);
        usDepartmentsMap.put(code, existingDept);

        Method method = WechatOrgchartServiceImpl.class.getDeclaredMethod("updateDeptTree", Map.class, String.class,
                                                                          String.class, String.class, String.class,
                                                                          List.class, List.class);
        method.setAccessible(true);

        // Act
        method.invoke(wechatOrgchartServiceImpl, usDepartmentsMap, parentCode, code, enName, cnName, addDeptList,
                      updateDeptList);

        // Assert
        assertEquals(1, updateDeptList.size());
        UsDepartment updatedDept = updateDeptList.get(0);

        expectedEnName = "null".equals(expectedEnName) ? null : expectedEnName;
        assertEquals(code, updatedDept.getSourceDeptId());
        assertEquals(parentCode, updatedDept.getSourceDeptParentId());
        assertEquals(expectedName, updatedDept.getName());
        assertEquals(expectedEnName, updatedDept.getNameEn());
    }

    @Test
    @DisplayName("校验字符串-正常场景：字符串长度在范围内且无特殊字符")
    void limitAndValidateString_validInput() {
        // Arrange
        String input = "ValidString";

        // Act
        String result = wechatOrgchartServiceImpl.limitAndValidateString(input);

        // Assert
        assertEquals(input, result);
    }

    @Test
    @DisplayName("校验字符串-异常场景：字符串长度超过64")
    void limitAndValidateString_exceedsLength() {
        // Arrange
        String input = "a".repeat(70);

        // Act
        String result = wechatOrgchartServiceImpl.limitAndValidateString(input);

        // Assert
        assertEquals(input.substring(6), result);
    }

    @Test
    @DisplayName("校验字符串-异常场景：字符串包含特殊字符")
    void limitAndValidateString_containsSpecialCharacters() {
        // Arrange
        String input = "Invalid\\String:With*Special?Characters";

        // Act
        String result = wechatOrgchartServiceImpl.limitAndValidateString(input);

        // Assert
        assertEquals(input, result);
    }

}