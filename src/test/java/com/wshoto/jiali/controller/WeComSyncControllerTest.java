package com.wshoto.jiali.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.wshoto.jiali.domain.UsDepartment;
import com.wshoto.jiali.domain.UsDepartmentUser;
import com.wshoto.jiali.domain.UsUser;
import com.wshoto.jiali.enums.DataStatusEnum;
import com.wshoto.jiali.enums.SyncStatusEnum;
import com.wshoto.jiali.mapper.UsDepartmentMapper;
import com.wshoto.jiali.service.DbManagerService;
import com.wshoto.jiali.service.UsDepartmentUserService;
import com.wshoto.jiali.service.UsNotifyConfigService;
import com.wshoto.jiali.service.UsUserService;
import com.wshoto.jiali.service.WechatActiveStaffinfoService;
import com.wshoto.jiali.service.WechatOrgchartService;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.wshoto.jiali.enums.DeleteStatusEnum.NOT_DELETED;
import static com.wshoto.jiali.enums.UserEnableEnum.ENABLE;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * AAD信息同步us_user-单元测试
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR>
 * Created Date - 2025-4-22
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AAD信息同步us_user-单元测试")
class WeComSyncControllerTest {

    @Mock
    private UsDepartmentMapper usDepartmentMapper;

    @Mock
    private UsNotifyConfigService usNotifyConfigService;

    @Mock
    private WechatActiveStaffinfoService wechatActiveStaffinfoService;

    @Mock
    private UsUserService userService;

    @Mock
    private WechatOrgchartService wechatOrgchartService;

    @Mock
    private UsDepartmentUserService usDepartmentUserService;

    @Mock
    private DbManagerService dbManagerService;

    @InjectMocks
    private WeComSyncController weComSyncController;

    @Test
    @DisplayName("部门初始化-正常场景")
    void initDept_success() {
        // Act
        weComSyncController.initDept();

        // Assert
        verify(wechatOrgchartService, times(1)).initOrgchart();
    }

    @Test
    @DisplayName("部门增量同步-正常场景")
    void syncDept_success() {
        // Act
        weComSyncController.syncDept();

        // Assert
        verify(wechatOrgchartService, times(1)).syncOrgchart();
    }

    @Test
    @DisplayName("员工同步-正常场景")
    void initUser_success() {
        // Act
        weComSyncController.initUser();

        // Assert
        verify(wechatActiveStaffinfoService, times(1)).syncStaffInfo();
    }

    @ParameterizedTest
    @ValueSource(strings = {"", "  "})
    @DisplayName("异常场景：sql为空")
    void adminExecuteSql_blankSql_shouldReturnError(String sql) {
        Object result = weComSyncController.adminExecuteSql(sql);
        assertEquals("sql不能为空", result);
    }

    @Test
    @DisplayName("异常场景：非查询语句")
    void adminExecuteSql_nonSelectQuery_shouldReturnFullResult() {
        // Arrange
        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("success", true);
        mockResult.put("affectedRows", 5);

        when(dbManagerService.executeSql(anyString(), anyString()))
                .thenReturn(mockResult);

        // Act
        Object result = weComSyncController.adminExecuteSql("UPDATE table SET col=1");

        // Assert
        assertSame(mockResult, result);
    }

    @Test
    @DisplayName("正常场景：查询语句")
    void adminExecuteSql_selectQuerySuccess_shouldReturnDataField() {
        // Arrange 构建SELECT成功场景
        Map<String, Object> mockData = Map.of("id", 1001);
        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("success", true);
        mockResult.put("data", mockData);

        when(dbManagerService.executeSql(anyString(), anyString()))
                .thenReturn(mockResult);

        // Act
        Object result = weComSyncController.adminExecuteSql("SELECT * FROM table");

        // Assert
        assertSame(mockData, result);
    }

    @Test
    @DisplayName("异常场景：查询语句失败")
    void adminExecuteSql_selectQueryFailure_shouldReturnOriginalResult() {
        // Arrange SELECT执行失败的两种情况
        Map<String, Object> failedResult1 = new HashMap<>();
        failedResult1.put("success", false);
        failedResult1.put("error", "Timeout");

        Map<String, Object> failedResult2 = new HashMap<>();
        failedResult2.put("error", "No connection");

        when(dbManagerService.executeSql(anyString(), anyString()))
                .thenReturn(failedResult1)
                .thenReturn(failedResult2);

        // 第一次调用
        Object result1 = weComSyncController.adminExecuteSql("select id from table");
        assertSame(failedResult1, result1);

        // 第二次调用
        Object result2 = weComSyncController.adminExecuteSql("select name from table");
        assertSame(failedResult2, result2);
    }

    @Test
    @DisplayName("异常场景：执行返回值为空")
    void adminExecuteSql_nullResult_shouldReturnErrorMessage() {
        // 处理空结果分支
        when(dbManagerService.executeSql(anyString(), anyString()))
                .thenReturn(null);

        Object result = weComSyncController.adminExecuteSql("DELETE FROM table");

        assertEquals("sql执行失败，结果为空", result);
    }

    @Test
    @DisplayName("正常场景：执行带空格和大写的SELECT语句")
    void adminExecuteSql_caseInsensitiveSelectCheck() {
        // 边界条件：带空格和大写的SELECT语句
        String[] testCases = {
                " SELECT * FROM table",  // 前导空格
                "SeLeCt * FROM table",   // 混合大小写
                "\tselect\n* FROM table" // 包含制表符和换行
        };

        for (String testSql : testCases) {
            Map<String, Object> mockResult = new HashMap<>();
            mockResult.put("success", true);
            mockResult.put("data", "test");

            when(dbManagerService.executeSql(anyString(), anyString()))
                    .thenReturn(mockResult);

            // Act
            Object result = weComSyncController.adminExecuteSql(testSql);

            // Assert
            assertEquals("test", result);
        }
    }

    @Test
    @DisplayName("重新推送香港部门-正常场景")
    void rePushHKDept_success() {
        // Act and Assert
        assertDoesNotThrow(() -> weComSyncController.rePushHKDept("123"));
    }

    @Test
    @DisplayName("设置通知邮箱-正常场景")
    void setNotifyEmail_success() {
        // Arrange
        String mockEmailAddress = "<EMAIL>";
        Map<String, String> request = Map.of("emailAddress", mockEmailAddress);
        when(usNotifyConfigService.setNotifyEmails(mockEmailAddress)).thenReturn(1);

        // Act
        int result = weComSyncController.setNotifyEmail(request);

        // Assert
        assertEquals(1, result);
        verify(usNotifyConfigService, times(1)).setNotifyEmails(mockEmailAddress);
    }

    @Test
    @DisplayName("设置通知邮箱-异常场景：邮箱地址为空")
    void setNotifyEmail_EmptyEmailAddressError() {
        // Arrange
        Map<String, String> request = Map.of("emailAddress", StringUtils.EMPTY);

        // Act
        int result = weComSyncController.setNotifyEmail(request);

        // Assert
        assertEquals(0, result);
    }

    @Test
    @DisplayName("手动退出员工-正常场景")
    void manuallyExitedStaff_success() {
        // Arrange
        String adUserName = "test-user";
        var expectedUser = new UsUser();
        expectedUser.setSourceUserid(adUserName);

        assertDoesNotThrow(() -> weComSyncController.manuallyExitedStaff("adUserName"));
    }

    @Test
    @DisplayName("删除通知配置-正常场景")
    void delNotifyConfig_success() {
        // Act
        weComSyncController.delNotifyConfig();

        // Assert
        verify(usNotifyConfigService, times(1)).deleteNotifyConfig();
    }

    @Test
    @DisplayName("手动禁用员工-正常场景")
    void manuallyDisableStaff_success() {
        // Act
        weComSyncController.manuallyDisableStaff("adUserName");

        // Assert
        verify(wechatActiveStaffinfoService, times(1)).manuallyDisableStaff(anyString());
    }

    @Test
    @DisplayName("查询部门信息-正常场景：通过目标部门ID")
    void queryDept_byTargetDeptId_success() {
        // Arrange
        String deptId = "123";
        var expectedDept = new UsDepartment();
        expectedDept.setTargetDeptId(deptId);

        // Act and Assert
        assertDoesNotThrow(() -> weComSyncController.queryDept(deptId));
    }

    @Test
    @DisplayName("查询部门信息-正常场景：通过源部门ID")
    void queryDept_bySourceDeptId_success() {
        // Arrange
        String deptId = "source-123";
        var expectedDept = new UsDepartment();
        expectedDept.setSourceDeptId(deptId);

        // Act and Assert
        assertDoesNotThrow(() -> weComSyncController.queryDept(deptId));
    }

    @Test
    @DisplayName("更新部门信息-正常场景")
    void updateDept_success() {
        // Arrange
        var department = new UsDepartment();
        department.setTargetDeptId("123");

        // Act
        weComSyncController.updateDept(department);

        // Assert
        verify(usDepartmentMapper, times(1)).updateById(department);
    }

    @Test
    @DisplayName("查询用户状态-正常场景")
    void queryUserByStatus_success() {
        // Arrange
        Integer dataStatus = DataStatusEnum.INSERT.getCode();
        Integer syncStatus = SyncStatusEnum.INIT.getCode();

        UsUser user = new UsUser();
        user.setId(1L);
        user.setDataStatus(dataStatus);
        user.setSyncStatus(syncStatus);

        // Mock the chain
        LambdaQueryChainWrapper<UsUser> mockQueryChainWrapper = Mockito.mock(LambdaQueryChainWrapper.class);
        when(userService.lambdaQuery()).thenReturn(mockQueryChainWrapper);
        when(mockQueryChainWrapper.eq(any(), anyInt())).thenReturn(mockQueryChainWrapper);
        when(mockQueryChainWrapper.eq(any(), anyInt())).thenReturn(mockQueryChainWrapper);

        List<UsUser> mockResult = List.of(new UsUser());
        when(mockQueryChainWrapper.list()).thenReturn(mockResult);


        // Act
        weComSyncController.queryUserByStatus(dataStatus, syncStatus);

        // Assert
        verify(userService, times(1)).lambdaQuery();
    }

    @Test
    @DisplayName("更新用户状态-正常场景：目标用户为空")
    void updateUsUserStatus_userListEmpty_success() {
        // Arrange
        LambdaQueryChainWrapper<UsUser> mockQueryChainWrapper = Mockito.mock(LambdaQueryChainWrapper.class);
        when(userService.lambdaQuery()).thenReturn(mockQueryChainWrapper);
        when(mockQueryChainWrapper.in(any(), anyString())).thenReturn(mockQueryChainWrapper);
        when(mockQueryChainWrapper.eq(any(), anyInt())).thenReturn(mockQueryChainWrapper);
        when(mockQueryChainWrapper.list()).thenReturn(Collections.emptyList());

        // Act
        Object result = weComSyncController.updateUsUserStatus(Collections.emptyMap());

        // Assert
        verify(userService, times(1)).lambdaQuery();
        assertNotNull(result);
    }

    @Test
    @DisplayName("更新用户状态-正常场景：只取一个")
    void updateUsUserStatus_stream_one_success() {
        // Arrange
        Map<String, Object> request = new HashMap<>();
        request.put("dataStatus", DataStatusEnum.INSERT.getCode());
        request.put("syncStatus", SyncStatusEnum.INIT.getCode());
        request.put("directLeader", "leader1");
        request.put("deleted", NOT_DELETED.getCode());
        request.put("enable", ENABLE.getCode());
        request.put("mainDepartment", 101);

        UsUser mockUser = new UsUser();
        mockUser.setId(1L);
        mockUser.setSourceUserid("user1");
        List<UsUser> dbUserList = List.of(mockUser);

        LambdaQueryChainWrapper<UsUser> mockQueryChainWrapper = Mockito.mock(LambdaQueryChainWrapper.class);
        when(userService.lambdaQuery()).thenReturn(mockQueryChainWrapper);
        when(mockQueryChainWrapper.in(any(), anyString())).thenReturn(mockQueryChainWrapper);
        when(mockQueryChainWrapper.eq(any(), anyInt())).thenReturn(mockQueryChainWrapper);
        when(mockQueryChainWrapper.list()).thenReturn(dbUserList);

        // Act
        Object result = weComSyncController.updateUsUserStatus(request);

        // Assert
        verify(userService, times(1)).updateById(mockUser);
        assertNotNull(result);
    }

    @Test
    @DisplayName("更新用户状态-正常场景：取列表中所有")
    void updateUsUserStatus_stream_all_success() {
        // Arrange
        Map<String, Object> request = new HashMap<>();
        request.put("all", 2);
        request.put("aad_id", 1);

        List<UsUser> dbUserList = List.of(new UsUser());

        LambdaQueryChainWrapper<UsUser> mockQueryChainWrapper = Mockito.mock(LambdaQueryChainWrapper.class);
        when(userService.lambdaQuery()).thenReturn(mockQueryChainWrapper);
        when(mockQueryChainWrapper.in(any(), anyString())).thenReturn(mockQueryChainWrapper);
        when(mockQueryChainWrapper.eq(any(), anyInt())).thenReturn(mockQueryChainWrapper);
        when(mockQueryChainWrapper.list()).thenReturn(dbUserList);

        // Act
        Object result = weComSyncController.updateUsUserStatus(request);

        // Assert
        verify(userService, times(2)).lambdaQuery();
        assertNotNull(result);
    }

    @Test
    @DisplayName("更新部门用户-正常场景：所有条件均匹配")
    void updateUsDepartUser_allConditionsMatch() {
        // Arrange
        Map<String, Object> request = new HashMap<>();
        request.put("deptId", "123");
        request.put("ad_username", "testUser");
        request.put("del", 0);

        var mockUser = new UsDepartmentUser();
        mockUser.setTargetDeptId("123");
        mockUser.setTartgetUserid("testUser");
        mockUser.setDeleted(0);

        List<UsDepartmentUser> mockResult = List.of(mockUser);
        when(usDepartmentUserService.list(any(LambdaQueryWrapper.class))).thenReturn(mockResult);

        // Act
        Object result = weComSyncController.updateUsDepartUser(request);

        // Assert
        assertEquals(mockResult, result);
        verify(usDepartmentUserService, times(1)).list(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("更新部门用户-正常场景")
    void updateUsDepartUser_success() {
        // Arrange
        var departmentUser = new UsDepartmentUser();

        // Act
        weComSyncController.updateUsDepartUser(departmentUser);

        // Assert
        verify(usDepartmentUserService, times(1)).updateById(departmentUser);
    }

    @Test
    @DisplayName("更新外部同步名称-正常场景")
    void updateExternalSync_success() {
        // Arrange
        String val = "newSyncName";

        // Act
        Object result = weComSyncController.updateExternalSync(val);

        // Assert
        assertNotNull(result);
    }

}