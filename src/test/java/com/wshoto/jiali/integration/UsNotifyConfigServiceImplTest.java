package com.wshoto.jiali.integration;

import com.wshoto.jiali.config.JiaLiConfig;
import com.wshoto.jiali.domain.UsNotifyConfig;
import com.wshoto.jiali.enums.NotifyTypeEnum;
import com.wshoto.jiali.service.UsNotifyConfigService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * UsNotifyConfigServiceImpl测试类
 *
 * <AUTHOR>
 * @date 2024-7-5
 */
public class UsNotifyConfigServiceImplTest extends AbstractIntegrationTest {

    @Autowired
    private UsNotifyConfigService usNotifyConfigService;

    @Autowired
    private JiaLiConfig jiaLiConfig;

    @Test
    public void _01_saveUsNotifyConfig_success() {
        // 待验证部分
        usNotifyConfigService.saveUsNotifyConfig();

        // 执行结果验证
        List<UsNotifyConfig> actualUsNotifyConfigs = usNotifyConfigService.list();
        UsNotifyConfig actualUsNotifyConfig = actualUsNotifyConfigs.get(0);

        Assertions.assertEquals(jiaLiConfig.getTenantId(), actualUsNotifyConfig.getTenantId());
        Assertions.assertEquals(NotifyTypeEnum.MAIL.getCode(), actualUsNotifyConfig.getNotifyType());
        Assertions.assertEquals(NotifyTypeEnum.MAIL.getCode(), actualUsNotifyConfig.getNotifyType());
        Assertions.assertEquals(jiaLiConfig.getMail(), actualUsNotifyConfig.getMail());
        Assertions.assertEquals(jiaLiConfig.getUsername(), actualUsNotifyConfig.getUsername());
        Assertions.assertEquals(jiaLiConfig.getPassword(), actualUsNotifyConfig.getPassword());
        Assertions.assertEquals(jiaLiConfig.getHost(), actualUsNotifyConfig.getHost());
        Assertions.assertEquals(Long.valueOf(jiaLiConfig.getPort()), actualUsNotifyConfig.getPort());
    }

}
