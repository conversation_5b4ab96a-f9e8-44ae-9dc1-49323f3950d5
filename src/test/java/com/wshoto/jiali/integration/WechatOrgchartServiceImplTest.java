package com.wshoto.jiali.integration;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wshoto.jiali.domain.UsDepartment;
import com.wshoto.jiali.domain.WechatOrgchart;
import com.wshoto.jiali.enums.DataStatusEnum;
import com.wshoto.jiali.service.impl.UsDepartmentServiceImpl;
import com.wshoto.jiali.service.impl.WechatOrgchartServiceImpl;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * WechatOrgchartServiceImpl测试类
 *
 * <AUTHOR>
 * @date 2024-7-5
 */
@TestMethodOrder(MethodOrderer.MethodName.class)
public class WechatOrgchartServiceImplTest extends AbstractIntegrationTest {

    @Autowired
    private WechatOrgchartServiceImpl wechatOrgchartService;

    @Autowired
    private UsDepartmentServiceImpl usDepartmentService;

    /**
     * 初始化部门信息
     */
    @Test
    public void _01_initOrgchart_success() {
        usDepartmentService.remove(Wrappers.emptyWrapper());

        // 待验证部分
        wechatOrgchartService.initOrgchart();

        // 执行结果验证
        List<UsDepartment> usDepartmentList = usDepartmentService.list();

        Assertions.assertEquals(10, usDepartmentList.size());
        Assertions.assertEquals(usDepartmentList.get(0).getSourceDeptId(), usDepartmentList.get(1).getSourceDeptParentId());
        Assertions.assertEquals(usDepartmentList.get(1).getSourceDeptId(), usDepartmentList.get(2).getSourceDeptParentId());
        Assertions.assertEquals(usDepartmentList.get(1).getSourceDeptId(), usDepartmentList.get(3).getSourceDeptParentId());
    }

    /**
     * 同步部门增量信息
     */
    @Test
    public void _02_syncOrgchart_success() {
        WechatOrgchart wechatOrgchartNew = new WechatOrgchart();
        wechatOrgchartNew.setL1GroupCode("KP");
        wechatOrgchartNew.setL1GroupEn("Kerry Properties");
        wechatOrgchartNew.setL1GroupCn("嘉里建设");

        wechatOrgchartNew.setL2DivisionCode("KPCND");
        wechatOrgchartNew.setL2DivisionEn("China");
        wechatOrgchartNew.setL2DivisionCn("中国大陆");

        wechatOrgchartNew.setL3BusinessGroupCode("KPBG-CNPJ");
        wechatOrgchartNew.setL3BusinessGroupEn("China Project");
        wechatOrgchartNew.setL3BusinessGroupCn("CN项目");

        wechatOrgchartNew.setL4BusinessUnitCode("KPBU-CCNPJ");
        wechatOrgchartNew.setL4BusinessUnitEn("Projects-Central China");
        wechatOrgchartNew.setL4BusinessUnitCn("CN项目-中区");

        wechatOrgchartNew.setL5ProjectCode("KPPJ-KCPMHZ-PJ");
        wechatOrgchartNew.setL5ProjectEn("Central China Centralised Function");
        wechatOrgchartNew.setL5ProjectCn("KPCPMHZ");

        wechatOrgchartNew.setL6LegalEntityCode("KPLE-KPCPM-HZ");
        wechatOrgchartNew.setL6LegalEntityEn("Kerry Properties (China) Project Management Co., Ltd.");
        wechatOrgchartNew.setL6LegalEntityCn("嘉里（中国）项目管理有限公司");

        wechatOrgchartNew.setL7FunctionCode("KPFN-ADM-KPCPMHZ");
        wechatOrgchartNew.setL7FunctionEn("Administration");
        wechatOrgchartNew.setL7FunctionCn("行政");

        wechatOrgchartNew.setL8DepartmentCode("KPDP-ADM-ADMKPCPMHZ");
        wechatOrgchartNew.setL8DepartmentEn("Administration");
        wechatOrgchartNew.setL8DepartmentCn("行政部");
        wechatOrgchartService.save(wechatOrgchartNew);

        WechatOrgchart wechatOrgchartDelete = wechatOrgchartService.list().get(0);
        wechatOrgchartService.remove(new LambdaQueryWrapper<WechatOrgchart>()
                .eq(WechatOrgchart::getL5ProjectCode, wechatOrgchartDelete.getL5ProjectCode()));

        WechatOrgchart wechatOrgchartUpdate = wechatOrgchartService.list().get(1);
        wechatOrgchartService.update(new LambdaUpdateWrapper<WechatOrgchart>()
                .eq(WechatOrgchart::getL7FunctionCode, wechatOrgchartUpdate.getL7FunctionCode())
                .set(WechatOrgchart::getL7FunctionEn, "KerryTest2")
                .set(WechatOrgchart::getL7FunctionCn, "嘉里测试2"));

        // 待验证部分
        wechatOrgchartService.syncOrgchart();

        // 执行结果验证
        List<UsDepartment> usDepartmentList = usDepartmentService.list();

        // 验证新增部门功能
        Assertions.assertEquals(1, usDepartmentList.stream()
                .filter(usDepartment -> wechatOrgchartNew.getL7FunctionCode().equals(usDepartment.getSourceDeptId()))
                .count());
        Assertions.assertEquals(1, usDepartmentList.stream()
                .filter(usDepartment -> wechatOrgchartNew.getL8DepartmentCode().equals(usDepartment.getSourceDeptId()))
                .count());

        // 验证删除部门功能
        List<UsDepartment> usDepartmentListL7Deleted = usDepartmentList.stream()
                .filter(usDepartment -> wechatOrgchartDelete.getL7FunctionCode().equals(usDepartment.getSourceDeptId())).toList();
        Assertions.assertEquals(1, usDepartmentListL7Deleted.size());
        Assertions.assertEquals(DataStatusEnum.DELETE.getCode(), usDepartmentListL7Deleted.get(0).getDataStatus());
        Assertions.assertEquals(DataStatusEnum.INIT.getCode(), usDepartmentListL7Deleted.get(0).getSyncStatus());

        List<UsDepartment> usDepartmentListL8Deleted = usDepartmentList.stream()
                .filter(usDepartment -> wechatOrgchartDelete.getL8DepartmentCode().equals(usDepartment.getSourceDeptId())).toList();
        Assertions.assertEquals(1, usDepartmentListL8Deleted.size());
        Assertions.assertEquals(DataStatusEnum.DELETE.getCode(), usDepartmentListL8Deleted.get(0).getDataStatus());
        Assertions.assertEquals(DataStatusEnum.INIT.getCode(), usDepartmentListL8Deleted.get(0).getSyncStatus());

        // 验证修改部门名称功能
        UsDepartment usDepartmentModified = usDepartmentList.stream()
                .filter(usDepartment -> wechatOrgchartUpdate.getL7FunctionCode().equals(usDepartment.getSourceDeptId()))
                .findFirst().get();
        Assertions.assertEquals("嘉里测试2", usDepartmentModified.getName());
        Assertions.assertEquals("KerryTest2", usDepartmentModified.getNameEn());
    }

}
