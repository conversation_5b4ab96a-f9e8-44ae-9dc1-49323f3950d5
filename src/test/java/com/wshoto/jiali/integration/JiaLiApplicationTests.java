package com.wshoto.jiali.integration;

import cn.hutool.core.date.DateUtil;
import com.wshoto.jiali.config.JiaLiConfig;
import com.wshoto.jiali.domain.UsDepartment;
import com.wshoto.jiali.domain.WechatOrgchart;
import com.wshoto.jiali.enums.DataStatusEnum;
import com.wshoto.jiali.service.UsDepartmentService;
import com.wshoto.jiali.service.WechatOrgchartService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

class JiaLiApplicationTests extends AbstractIntegrationTest {

    @Autowired
    private WechatOrgchartService wechatOrgchartService;

    @Autowired
    private JiaLiConfig jiaLiConfig;

    @Autowired
    private UsDepartmentService usDepartmentService;

    @Test
    @DisplayName("租户ID和部门白名单配置正确")
    void jiaLiApolloConfigShouldBeOk() {
        String tenantId = jiaLiConfig.getTenantId();
        List<String> whiteDept = jiaLiConfig.getWhiteDept();
        assertThat(whiteDept).isNotEmpty()
                .asList()
                .isEqualTo(List.of("KPDP-RMIR-OO", "KPPJ-HKPpppp", "KPDP-TI-TISHKPCPMNM", "KPDP-KPFIN-COD", "KPDP-NOT-EXIST"));
        assertThat(tenantId).isEqualTo("ww6ffc4f642bca6ae8");

    }

    @Test
    @DisplayName("us_department(部门同步表)保存应该正常")
    void shouldOkWhenSaveUsDepartment() {
        List<UsDepartment> usDepartmentList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            UsDepartment usDepartment = new UsDepartment();
            usDepartment.setSourceDeptId("ada");
            usDepartment.setSourceDeptParentId("eqw");
            usDepartment.setName("hasodhao");
            usDepartment.setNameEn("weqw");
            usDepartment.setDataStatus(DataStatusEnum.INSERT.getCode());
            usDepartment.setSyncStatus(DataStatusEnum.INIT.getCode());
            usDepartment.setGmtCreate(DateUtil.date());
            usDepartment.setGmtModified(DateUtil.date());
            usDepartmentList.add(usDepartment);
        }
        var savedBatch = usDepartmentService.saveBatch(usDepartmentList);

        assertTrue(savedBatch, "us_department(部门同步表)保存应该正常");
    }

    @Test
    void dbTest() {
        List<WechatOrgchart> list = wechatOrgchartService.list();
        Map<String, List<WechatOrgchart>> collect = list.stream().collect(Collectors.groupingBy(WechatOrgchart::getL1GroupCode));
        assertThat(collect).as("数据库有数据").isNotEmpty();
    }

}
